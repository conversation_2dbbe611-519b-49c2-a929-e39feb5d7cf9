import { NextRequest, NextResponse } from 'next/server'
import prisma from '@/lib/prisma'
import { requireAdmin } from '@/lib/auth'

// GET all settings or specific settings by keys
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const keys = searchParams.get('keys')?.split(',')

    if (keys && keys.length > 0) {
      // Get specific settings by keys
      const settings = await prisma.setting.findMany({
        where: {
          key: {
            in: keys
          }
        }
      })

      // Convert to key-value object
      const settingsObject = settings.reduce((acc: any, setting: any) => {
        try {
          // Try to parse as JSO<PERSON> first (since database has json_valid constraint)
          acc[setting.key] = JSON.parse(setting.value)
        } catch {
          // If parsing fails, use the value as-is
          acc[setting.key] = setting.value
        }
        return acc
      }, {} as Record<string, any>)

      return NextResponse.json(settingsObject)
    } else {
      // Get all settings
      const settings = await prisma.setting.findMany()

      // Convert to key-value object
      const settingsObject = settings.reduce((acc: any, setting: any) => {
        try {
          // Try to parse as JSO<PERSON> first (since database has json_valid constraint)
          acc[setting.key] = JSON.parse(setting.value)
        } catch {
          // If parsing fails, use the value as-is
          acc[setting.key] = setting.value
        }
        return acc
      }, {} as Record<string, any>)

      const response = NextResponse.json(settingsObject)

      // AGGRESSIVE cache prevention with timestamp
      response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0')
      response.headers.set('Pragma', 'no-cache')
      response.headers.set('Expires', 'Thu, 01 Jan 1970 00:00:00 GMT')
      response.headers.set('Last-Modified', new Date().toUTCString())
      response.headers.set('X-Data-Timestamp', Date.now().toString())
      response.headers.set('X-Cache-Prevention', 'true')

      return response
    }
  } catch (error) {
    console.error('Error fetching settings:', error)
    return NextResponse.json(
      { error: 'Failed to fetch settings' },
      { status: 500 }
    )
  }
}

// POST/PUT update settings (bulk update)
export async function POST(request: NextRequest) {
  try {
    // Require admin authentication
    const authResult = await requireAdmin(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Not authenticated' ? 401 : 403 }
      )
    }

    const settings = await request.json()
    console.log(`Saving ${Object.keys(settings).length} settings...`)

    // Log color settings specifically (only if they're being updated)
    const colorSettings = ['primaryColor', 'secondaryColor', 'accentColor', 'darkColor']
    const colorUpdates = colorSettings.filter(key => settings[key])
    if (colorUpdates.length > 0) {
      console.log('Color updates:', colorUpdates.map(key => `${key}=${settings[key]}`).join(', '))
    }

    // Update each setting one by one with error handling
    const updatedSettings: string[] = []
    const failedSettings: string[] = []

    for (const [key, value] of Object.entries(settings)) {
      try {
        // Store value as JSON since the database has a json_valid constraint
        const jsonValue = JSON.stringify(value)

        await prisma.setting.upsert({
          where: { key },
          update: {
            value: jsonValue

          },
          create: {

            key,
            value: jsonValue,
            createdAt: new Date()

          }
        })

        updatedSettings.push(key)
      } catch (settingError) {
        console.error(`Error updating setting ${key}:`, settingError)
        failedSettings.push(key)
        // Continue with other settings even if one fails
      }
    }

    console.log(`Saved: ${updatedSettings.length} settings${failedSettings.length > 0 ? `, Failed: ${failedSettings.length}` : ''}`)

    return NextResponse.json({
      success: true,
      message: 'Settings updated successfully',
      updated: updatedSettings,
      failed: failedSettings
    })
  } catch (error) {
    console.error('Error updating settings:', error)
    return NextResponse.json(
      { error: `Failed to update settings: ${error instanceof Error ? error.message : 'Unknown error'}` },
      { status: 500 }
    )
  }
}

// DELETE specific settings
export async function DELETE(request: NextRequest) {
  try {
    // Require admin authentication
    const authResult = await requireAdmin(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Not authenticated' ? 401 : 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const keys = searchParams.get('keys')?.split(',')

    if (!keys || keys.length === 0) {
      return NextResponse.json(
        { error: 'No keys provided' },
        { status: 400 }
      )
    }

    await prisma.setting.deleteMany({
      where: {
        key: {
          in: keys
        }
      }
    })

    return NextResponse.json({ success: true, message: 'Settings deleted successfully' })
  } catch (error) {
    console.error('Error deleting settings:', error)
    return NextResponse.json(
      { error: 'Failed to delete settings' },
      { status: 500 }
    )
  }
}
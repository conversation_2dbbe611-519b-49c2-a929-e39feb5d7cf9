#!/bin/bash

# Quick Production Upload Fix
# Creates symlink to fix upload path mismatch

echo "🔗 Quick Production Upload Fix"
echo "=============================="

# Check if we're in the right directory
if [ ! -d ".next/standalone" ]; then
    echo "❌ .next/standalone not found. Run this from your project root."
    exit 1
fi

# Create standalone public directory if it doesn't exist
mkdir -p .next/standalone/public

# Create uploads directory in standalone
mkdir -p .next/standalone/public/uploads

# Create symlink from standalone uploads to main uploads
if [ -d "public/uploads" ]; then
    echo "🔗 Creating symlink from standalone to main uploads directory..."
    ln -sf ../../../../public/uploads .next/standalone/public/uploads
    echo "✅ Symlink created successfully"
else
    echo "📁 Creating uploads directory..."
    mkdir -p public/uploads
    mkdir -p .next/standalone/public/uploads/products
    mkdir -p .next/standalone/public/uploads/categories
fi

# Set permissions
chmod -R 755 .next/standalone/public/
chmod -R 755 public/uploads/

echo ""
echo "🚀 Fix applied! Now:"
echo "1. Restart your app: pm2 restart horseoud"
echo "2. Reload nginx: sudo systemctl reload nginx"
echo "3. Test upload in admin panel"
echo ""
echo "✅ Uploads should now work correctly!"

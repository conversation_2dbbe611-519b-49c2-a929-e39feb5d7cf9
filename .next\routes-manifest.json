{"version": 3, "caseSensitive": false, "basePath": "", "rewrites": {"beforeFiles": [], "afterFiles": [], "fallback": []}, "redirects": [{"source": "/:path+/", "destination": "/:path+", "permanent": true, "internal": true, "regex": "^(?:\\/((?:[^\\/]+?)(?:\\/(?:[^\\/]+?))*))\\/$"}], "headers": [{"source": "/_next/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}, {"key": "X-Content-Type-Options", "value": "nosniff"}], "regex": "^\\/_next\\/static(?:\\/(.*))(?:\\/)?$"}, {"source": "/_next/static/chunks/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}, {"key": "X-Content-Type-Options", "value": "nosniff"}], "regex": "^\\/_next\\/static\\/chunks(?:\\/(.*))(?:\\/)?$"}, {"source": "/_next/static/css/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}, {"key": "X-Content-Type-Options", "value": "nosniff"}], "regex": "^\\/_next\\/static\\/css(?:\\/(.*))(?:\\/)?$"}, {"source": "/_next/image(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=3600, s-maxage=86400"}], "regex": "^\\/_next\\/image(.*)(?:\\/)?$"}, {"source": "/((?!_next/static|_next/image|favicon.ico|uploads).*)", "headers": [{"key": "Cache-Control", "value": "no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "Thu, 01 Jan 1970 00:00:00 GMT"}, {"key": "Last-Modified", "value": "Sun, 31 Aug 2025 13:48:26 GMT"}], "regex": "^(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|uploads).*))(?:\\/)?$"}]}
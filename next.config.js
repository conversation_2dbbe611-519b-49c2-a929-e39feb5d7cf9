/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'instagram.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'scontent.cdninstagram.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'via.placeholder.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'placehold.co',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        port: '',
        pathname: '/**',
      },
    ],
  },
  // Smart cache control - prevents dynamic content caching but allows static assets
  async headers() {
    return [
      // Cache static assets properly for production
      {
        source: '/_next/static/(.*)',
        headers: [
          { key: 'Cache-Control', value: 'public, max-age=31536000, immutable' },
          { key: 'X-Content-Type-Options', value: 'nosniff' },
        ],
      },
      // Cache CSS and JS chunks for performance
      {
        source: '/_next/static/chunks/(.*)',
        headers: [
          { key: 'Cache-Control', value: 'public, max-age=31536000, immutable' },
          { key: 'X-Content-Type-Options', value: 'nosniff' },
        ],
      },
      // Cache CSS files properly
      {
        source: '/_next/static/css/(.*)',
        headers: [
          { key: 'Cache-Control', value: 'public, max-age=31536000, immutable' },
          { key: 'X-Content-Type-Options', value: 'nosniff' },
        ],
      },
      // Allow reasonable caching for images
      {
        source: '/_next/image(.*)',
        headers: [
          { key: 'Cache-Control', value: 'public, max-age=3600, s-maxage=86400' },
        ],
      },
      // NEVER cache dynamic content (API routes and pages)
      {
        source: '/((?!_next/static|_next/image|favicon.ico|uploads).*)',
        headers: [
          { key: 'Cache-Control', value: 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0' },
          { key: 'Pragma', value: 'no-cache' },
          { key: 'Expires', value: 'Thu, 01 Jan 1970 00:00:00 GMT' },
          { key: 'Last-Modified', value: new Date().toUTCString() },
        ],
      },
    ];
  },
  // Disable ETags and other caching mechanisms
  generateEtags: false,
  // External packages for server components (moved from experimental in Next.js 15+)
  serverExternalPackages: [],
  // Production optimizations
  output: 'standalone',
  
  // Optimize static exports for production
  distDir: '.next',
  
  // Ensure proper static file handling
  trailingSlash: false,
  
  // Production asset optimization
  compress: true,
}

module.exports = nextConfig
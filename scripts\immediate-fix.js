#!/usr/bin/env node

/**
 * Immediate Production Upload Fix
 * Creates symlink to fix path mismatch instantly
 */

const fs = require('fs').promises
const path = require('path')

async function main() {
  console.log('🔗 Immediate Production Upload Fix')
  console.log('==================================')

  try {
    // Check if standalone directory exists
    const standalonePublic = '.next/standalone/public'
    const uploadsDir = 'public/uploads'

    // Create standalone public if it doesn't exist
    try {
      await fs.mkdir(standalonePublic, { recursive: true })
      console.log('✅ Created standalone public directory')
    } catch (error) {
      // Directory might already exist
    }

    // Create uploads directory in standalone
    const standaloneUploads = path.join(standalonePublic, 'uploads')
    try {
      await fs.mkdir(standaloneUploads, { recursive: true })
      console.log('✅ Created standalone uploads directory')
    } catch (error) {
      // Directory might already exist
    }

    // Check if main uploads exist
    try {
      await fs.access(uploadsDir)
      console.log('✅ Main uploads directory exists')

      // Create symlink
      const { execSync } = require('child_process')
      execSync(`ln -sf ../../../../${uploadsDir} ${standaloneUploads}`)
      console.log('✅ Symlink created successfully')

    } catch (error) {
      console.log('⚠️  Main uploads directory not found, creating empty structure')
      await fs.mkdir(path.join(standaloneUploads, 'products'), { recursive: true })
      await fs.mkdir(path.join(standaloneUploads, 'categories'), { recursive: true })
    }

    // Set permissions
    try {
      const { execSync } = require('child_process')
      execSync(`chmod -R 755 ${standalonePublic}`)
      console.log('✅ Permissions set')
    } catch (error) {
      console.log('⚠️  Could not set permissions automatically')
    }

    console.log('')
    console.log('🚀 IMMEDIATE FIX APPLIED!')
    console.log('Run these commands on your server:')
    console.log('1. pm2 restart horseoud')
    console.log('2. sudo systemctl reload nginx')
    console.log('3. Test upload in admin panel')
    console.log('')
    console.log('✅ Uploads should work immediately!')

  } catch (error) {
    console.error('❌ Fix failed:', error.message)
    console.log('')
    console.log('Manual commands to run on server:')
    console.log('mkdir -p .next/standalone/public/uploads')
    console.log('ln -sf ../../../../public/uploads .next/standalone/public/uploads')
    console.log('chmod -R 755 .next/standalone/public/')
    console.log('pm2 restart horseoud && sudo systemctl reload nginx')
  }
}

main()

# 🔧 Fix 502 Errors in Proxy Setup

## 🎯 **Your Current Setup**
- ✅ Next.js app runs on `localhost:3000` (or another port)
- ✅ Nginx proxy forwards `horseoud.com` → `localhost:3000`
- ❌ **Problem**: Proxy forwards ALL requests, including static files

## 🚨 **The Issue**

Your nginx is doing this:
```
horseoud.com/_next/static/chunks/main.js → proxy → localhost:3000/_next/static/chunks/main.js
```

But it should do this:
```
horseoud.com/_next/static/chunks/main.js → serve directly from filesystem
horseoud.com/api/products → proxy → localhost:3000/api/products
```

## ⚡ **The Fix**

### **Step 1: Update Nginx Configuration**

Replace your current nginx server block with the configuration from `CORRECT-proxy-nginx.conf`.

**Key changes:**
1. **Static assets** (`/_next/static/`) → Served directly from filesystem
2. **Dynamic requests** (`/`, `/api/`, etc.) → Proxied to localhost:3000

### **Step 2: Verify Your App Port**

Check which port your Next.js app is running on:

```bash
# On your production server
ps aux | grep node
# or
netstat -tulpn | grep node
```

Update the proxy_pass line if needed:
```nginx
proxy_pass http://127.0.0.1:3000;  # Change 3000 to your actual port
```

### **Step 3: Apply the Fix**

```bash
# 1. Backup current config
sudo cp /etc/nginx/sites-available/horseoud /etc/nginx/sites-available/horseoud.backup

# 2. Edit configuration
sudo nano /etc/nginx/sites-available/horseoud

# 3. Test configuration
sudo nginx -t

# 4. Reload nginx
sudo systemctl reload nginx
```

### **Step 4: Test the Fix**

```bash
# Test static file (should return 200, not 502)
curl -I https://horseoud.com/_next/static/chunks/2619-04bc32f026a0d946.js

# Test dynamic request (should still work)
curl -I https://horseoud.com/api/products
```

## 🎯 **Expected Results**

**Before Fix:**
```
GET /_next/static/chunks/main.js → 502 (proxied to localhost, fails)
GET /order/123 → 200 (proxied to localhost, works)
```

**After Fix:**
```
GET /_next/static/chunks/main.js → 200 (served directly from filesystem)
GET /order/123 → 200 (proxied to localhost, still works)
```

## 🔍 **Troubleshooting**

### **If static files still fail:**

1. **Check file paths exist:**
   ```bash
   ls -la /www/wwwroot/node/ecommerc/.next/standalone/.next/static/chunks/
   ```

2. **Check permissions:**
   ```bash
   chmod -R 755 /www/wwwroot/node/ecommerc/.next/standalone
   ```

3. **Check nginx error log:**
   ```bash
   sudo tail -f /var/log/nginx/error.log
   ```

### **If dynamic requests fail:**

1. **Verify app is running:**
   ```bash
   curl http://localhost:3000
   ```

2. **Check app logs:**
   ```bash
   pm2 logs horseoud  # if using PM2
   ```

## ✅ **Success Indicators**

After the fix:
- ✅ No 502 errors in browser console
- ✅ CSS loads and pages are styled
- ✅ JavaScript functionality works
- ✅ Static assets return 200 status codes
- ✅ Dynamic pages still load correctly

## 🎯 **Why This Works**

**Static Assets (Fast):**
`/_next/static/*` → Nginx serves directly from disk

**Dynamic Content (Proxied):**
`/api/*`, `/order/*`, etc. → Nginx forwards to your localhost Node.js app

This gives you the **best of both worlds**: fast static asset serving + dynamic content from your app!

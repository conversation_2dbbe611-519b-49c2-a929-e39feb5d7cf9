/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/categories/route";
exports.ids = ["app/api/categories/route"];
exports.modules = {

/***/ "(rsc)/./app/api/categories/route.ts":
/*!*************************************!*\
  !*** ./app/api/categories/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var next_cache__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/cache */ \"(rsc)/./node_modules/next/cache.js\");\n/* harmony import */ var next_cache__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_cache__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\n// GET all categories\nasync function GET(request) {\n    try {\n        const searchParams = request.nextUrl.searchParams;\n        const status = searchParams.get('status');\n        const search = searchParams.get('search');\n        const showInMenu = searchParams.get('showInMenu');\n        const showInHomepage = searchParams.get('showInHomepage');\n        const where = {};\n        if (status && status !== 'all') {\n            where.status = status;\n        } else if (!status) {\n            // Default to active if no status is specified and we're filtering by showInMenu or showInHomepage\n            if (showInMenu === 'true' || showInHomepage === 'true') {\n                where.status = 'active';\n            }\n        }\n        if (search) {\n            where.OR = [\n                {\n                    nameAr: {\n                        contains: search\n                    }\n                },\n                {\n                    nameEn: {\n                        contains: search\n                    }\n                },\n                {\n                    slug: {\n                        contains: search\n                    }\n                }\n            ];\n        }\n        // Add filters for showInMenu and showInHomepage\n        if (showInMenu === 'true') {\n            where.showInMenu = true;\n        }\n        if (showInHomepage === 'true') {\n            where.showInHomepage = true;\n        }\n        const categories = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__[\"default\"].category.findMany({\n            where,\n            orderBy: [\n                {\n                    order: 'asc'\n                },\n                {\n                    createdAt: 'desc'\n                }\n            ],\n            include: {\n                _count: {\n                    select: {\n                        product: true\n                    }\n                }\n            }\n        });\n        const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            categories\n        });\n        // AGGRESSIVE cache prevention with timestamp\n        response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0');\n        response.headers.set('Pragma', 'no-cache');\n        response.headers.set('Expires', 'Thu, 01 Jan 1970 00:00:00 GMT');\n        response.headers.set('Last-Modified', new Date().toUTCString());\n        response.headers.set('X-Data-Timestamp', Date.now().toString());\n        response.headers.set('X-Cache-Prevention', 'true');\n        return response;\n    } catch (error) {\n        console.error('Error fetching categories:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch categories'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST create new category\nasync function POST(request) {\n    try {\n        // Require admin authentication\n        const authResult = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.requireAdmin)(request);\n        if (!authResult.success) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: authResult.error\n            }, {\n                status: authResult.error === 'Not authenticated' ? 401 : 403\n            });\n        }\n        const body = await request.json();\n        // Check if slug already exists\n        const existingCategory = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__[\"default\"].category.findUnique({\n            where: {\n                slug: body.slug\n            }\n        });\n        if (existingCategory) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Category with this slug already exists'\n            }, {\n                status: 400\n            });\n        }\n        const category = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__[\"default\"].category.create({\n            data: {\n                nameAr: body.nameAr,\n                nameEn: body.nameEn,\n                slug: body.slug,\n                description: body.description,\n                image: body.image,\n                order: body.order || 1,\n                status: body.status || 'active',\n                showInMenu: body.showInMenu !== undefined ? body.showInMenu : true,\n                showInHomepage: body.showInHomepage !== undefined ? body.showInHomepage : true,\n                metaTitle: body.metaTitle,\n                metaDescription: body.metaDescription\n            }\n        });\n        // Revalidate pages that display categories to clear cache\n        (0,next_cache__WEBPACK_IMPORTED_MODULE_3__.revalidatePath)('/'); // Home page\n        (0,next_cache__WEBPACK_IMPORTED_MODULE_3__.revalidatePath)('/products'); // Products page\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(category, {\n            status: 201\n        });\n    } catch (error) {\n        console.error('Error creating category:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to create category'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/categories/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AUTH_STORAGE_KEY: () => (/* binding */ AUTH_STORAGE_KEY),\n/* harmony export */   cleanupExpiredSessions: () => (/* binding */ cleanupExpiredSessions),\n/* harmony export */   clearClientAuthState: () => (/* binding */ clearClientAuthState),\n/* harmony export */   createSession: () => (/* binding */ createSession),\n/* harmony export */   generateSessionToken: () => (/* binding */ generateSessionToken),\n/* harmony export */   getClientAuthState: () => (/* binding */ getClientAuthState),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getUserSessions: () => (/* binding */ getUserSessions),\n/* harmony export */   invalidateSession: () => (/* binding */ invalidateSession),\n/* harmony export */   requireAdmin: () => (/* binding */ requireAdmin),\n/* harmony export */   requireUserOrAdmin: () => (/* binding */ requireUserOrAdmin),\n/* harmony export */   setClientAuthState: () => (/* binding */ setClientAuthState)\n/* harmony export */ });\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n// Secure authentication utilities\n\n\n/**\r\n * Generate a cryptographically secure session token\r\n */ function generateSessionToken() {\n    return (0,crypto__WEBPACK_IMPORTED_MODULE_0__.randomBytes)(32).toString('hex');\n}\n/**\r\n * Create a new session in the database\r\n */ async function createSession(userId, userAgent, ipAddress, rememberMe = false) {\n    const token = generateSessionToken();\n    const expiresAt = new Date();\n    // Set expiration based on remember me preference\n    if (rememberMe) {\n        expiresAt.setDate(expiresAt.getDate() + 30); // 30 days\n    } else {\n        expiresAt.setDate(expiresAt.getDate() + 1); // 1 day\n    }\n    await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__[\"default\"].session.create({\n        data: {\n            token,\n            userId,\n            userAgent,\n            ipAddress,\n            expiresAt\n        }\n    });\n    return {\n        token,\n        expiresAt\n    };\n}\n/**\r\n * Server-side function to get current user from session token\r\n * Use this in API routes for authentication\r\n */ async function getCurrentUser(request) {\n    try {\n        // Get session cookie (port-specific for development)\n        const port = request.headers.get('host')?.split(':')[1] || '3000';\n        const sessionCookieName = `session-${port}`;\n        const sessionCookie = request.cookies.get(sessionCookieName);\n        if (!sessionCookie) {\n            return {\n                success: false,\n                error: 'Not authenticated'\n            };\n        }\n        // Get session from database using token\n        const session = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__[\"default\"].session.findUnique({\n            where: {\n                token: sessionCookie.value\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        nameAr: true,\n                        nameEn: true,\n                        phone: true,\n                        role: true,\n                        isActive: true,\n                        emailVerified: true\n                    }\n                }\n            }\n        });\n        if (!session) {\n            return {\n                success: false,\n                error: 'Invalid session'\n            };\n        }\n        // Check if session has expired\n        if (session.expiresAt < new Date()) {\n            // Clean up expired session\n            await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__[\"default\"].session.delete({\n                where: {\n                    id: session.id\n                }\n            });\n            return {\n                success: false,\n                error: 'Session expired'\n            };\n        }\n        if (!session.user) {\n            return {\n                success: false,\n                error: 'User not found'\n            };\n        }\n        if (!session.user.isActive) {\n            return {\n                success: false,\n                error: 'Account is not active'\n            };\n        }\n        // Update session last accessed time\n        await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__[\"default\"].session.update({\n            where: {\n                id: session.id\n            },\n            data: {\n                updatedAt: new Date()\n            }\n        });\n        return {\n            success: true,\n            user: session.user\n        };\n    } catch (error) {\n        console.error('Error getting current user:', error);\n        return {\n            success: false,\n            error: 'Authentication error'\n        };\n    }\n}\n/**\r\n * Check if user has admin role\r\n * Always validates against database, never trusts client data\r\n */ async function requireAdmin(request) {\n    const authResult = await getCurrentUser(request);\n    if (!authResult.success || !authResult.user) {\n        return authResult;\n    }\n    if (authResult.user.role !== 'admin') {\n        return {\n            success: false,\n            error: 'Admin access required'\n        };\n    }\n    return authResult;\n}\n/**\r\n * Check if user has access to resource (either admin or owns the resource)\r\n */ async function requireUserOrAdmin(request, resourceUserId) {\n    const authResult = await getCurrentUser(request);\n    if (!authResult.success || !authResult.user) {\n        return authResult;\n    }\n    const user = authResult.user;\n    // Admin can access anything\n    if (user.role === 'admin') {\n        return authResult;\n    }\n    // User can only access their own resources\n    if (user.id === resourceUserId) {\n        return authResult;\n    }\n    return {\n        success: false,\n        error: 'Access denied'\n    };\n}\n/**\r\n * Invalidate a session (logout)\r\n */ async function invalidateSession(token) {\n    try {\n        await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__[\"default\"].session.delete({\n            where: {\n                token\n            }\n        });\n    } catch (error) {\n        // Session might already be deleted, which is fine\n        console.error('Error invalidating session:', error);\n    }\n}\n/**\r\n * Clean up expired sessions (should be run periodically)\r\n */ async function cleanupExpiredSessions() {\n    try {\n        const result = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__[\"default\"].session.deleteMany({\n            where: {\n                expiresAt: {\n                    lt: new Date()\n                }\n            }\n        });\n        return result.count;\n    } catch (error) {\n        console.error('Error cleaning up expired sessions:', error);\n        return 0;\n    }\n}\n/**\r\n * Get all active sessions for a user\r\n */ async function getUserSessions(userId) {\n    try {\n        return await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__[\"default\"].session.findMany({\n            where: {\n                userId,\n                expiresAt: {\n                    gt: new Date()\n                }\n            },\n            select: {\n                id: true,\n                userAgent: true,\n                ipAddress: true,\n                createdAt: true,\n                updatedAt: true,\n                expiresAt: true\n            },\n            orderBy: {\n                updatedAt: 'desc'\n            }\n        });\n    } catch (error) {\n        console.error('Error getting user sessions:', error);\n        return [];\n    }\n}\n// This will be used by a React hook to replace localStorage user data\nconst AUTH_STORAGE_KEY = 'auth_state';\nfunction getClientAuthState() {\n    if (true) {\n        return {\n            isAuthenticated: false,\n            isAdmin: false,\n            userId: null,\n            loading: true\n        };\n    }\n    try {\n        const stored = localStorage.getItem(AUTH_STORAGE_KEY);\n        if (stored) {\n            return JSON.parse(stored);\n        }\n    } catch (error) {\n        console.error('Error reading auth state:', error);\n        localStorage.removeItem(AUTH_STORAGE_KEY);\n    }\n    return {\n        isAuthenticated: false,\n        isAdmin: false,\n        userId: null,\n        loading: false\n    };\n}\nfunction setClientAuthState(state) {\n    if (true) return;\n    try {\n        localStorage.setItem(AUTH_STORAGE_KEY, JSON.stringify(state));\n    } catch (error) {\n        console.error('Error storing auth state:', error);\n    }\n}\nfunction clearClientAuthState() {\n    if (true) return;\n    localStorage.removeItem(AUTH_STORAGE_KEY);\n    localStorage.removeItem('user'); // Remove old insecure storage\n    localStorage.removeItem('rememberedEmail'); // Remove old insecure remember me\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst prismaClient = global.__prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) {\n    global.__prisma = prismaClient;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (prismaClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQU03QyxNQUFNQyxlQUFlQyxPQUFPQyxRQUFRLElBQUksSUFBSUgsd0RBQVlBO0FBRXhELElBQUlJLElBQXFDLEVBQUU7SUFDekNGLE9BQU9DLFFBQVEsR0FBR0Y7QUFDcEI7QUFFQSxpRUFBZUEsWUFBWUEsRUFBQSIsInNvdXJjZXMiOlsiRDpcXG5vZGVcXHdvbzJcXGxpYlxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50J1xuXG5kZWNsYXJlIGdsb2JhbCB7XG4gIHZhciBfX3ByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmNvbnN0IHByaXNtYUNsaWVudCA9IGdsb2JhbC5fX3ByaXNtYSB8fCBuZXcgUHJpc21hQ2xpZW50KClcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIHtcbiAgZ2xvYmFsLl9fcHJpc21hID0gcHJpc21hQ2xpZW50XG59XG5cbmV4cG9ydCBkZWZhdWx0IHByaXNtYUNsaWVudCJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJwcmlzbWFDbGllbnQiLCJnbG9iYWwiLCJfX3ByaXNtYSIsInByb2Nlc3MiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcategories%2Froute&page=%2Fapi%2Fcategories%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcategories%2Froute.ts&appDir=D%3A%5Cnode%5Cwoo2%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cnode%5Cwoo2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcategories%2Froute&page=%2Fapi%2Fcategories%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcategories%2Froute.ts&appDir=D%3A%5Cnode%5Cwoo2%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cnode%5Cwoo2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var D_node_woo2_app_api_categories_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./app/api/categories/route.ts */ \"(rsc)/./app/api/categories/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/categories/route\",\n        pathname: \"/api/categories\",\n        filename: \"route\",\n        bundlePath: \"app/api/categories/route\"\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    resolvedPagePath: \"D:\\\\node\\\\woo2\\\\app\\\\api\\\\categories\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_node_woo2_app_api_categories_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/categories/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcategories%2Froute&page=%2Fapi%2Fcategories%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcategories%2Froute.ts&appDir=D%3A%5Cnode%5Cwoo2%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cnode%5Cwoo2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcategories%2Froute&page=%2Fapi%2Fcategories%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcategories%2Froute.ts&appDir=D%3A%5Cnode%5Cwoo2%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cnode%5Cwoo2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();
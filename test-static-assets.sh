#!/bin/bash

echo "🧪 Testing Static Asset Fix"
echo "=========================="

# Test the specific files that were failing
echo "Testing files that were showing 502 errors..."
echo ""

# Test CSS file
echo "📄 Testing CSS file:"
curl -s -I "https://horseoud.com/_next/static/css/5bac398b91f1b183.css" | head -1
if curl -s -I "https://horseoud.com/_next/static/css/5bac398b91f1b183.css" | grep -q "200 OK"; then
    echo "✅ CSS file: SUCCESS"
else
    echo "❌ CSS file: STILL FAILING"
fi

echo ""

# Test JS chunks
echo "📄 Testing JavaScript chunks:"
JS_FILES=(
    "2619-04bc32f026a0d946.js"
    "main-app-a1f1f5d698df6a05.js"
    "webpack-93bca1d962f795f5.js"
    "4bd1b696-100b9d70ed4e49c1.js"
)

for file in "${JS_FILES[@]}"; do
    echo -n "   $file: "
    if curl -s -I "https://horseoud.com/_next/static/chunks/$file" | grep -q "200 OK"; then
        echo "✅ SUCCESS"
    else
        echo "❌ FAILING"
    fi
done

echo ""

# Test favicon
echo "📄 Testing favicon:"
echo -n "   favicon.ico: "
if curl -s -I "https://horseoud.com/favicon.ico" | grep -q -E "(200 OK|404)"; then
    echo "✅ SUCCESS (200 or 404 is OK, but not 502)"
else
    echo "❌ STILL 502"
fi

echo ""
echo "🎯 Summary:"
echo "==========="

# Count successful tests
SUCCESS_COUNT=0
TOTAL_COUNT=6

if curl -s -I "https://horseoud.com/_next/static/css/5bac398b91f1b183.css" | grep -q "200 OK"; then
    ((SUCCESS_COUNT++))
fi

for file in "${JS_FILES[@]}"; do
    if curl -s -I "https://horseoud.com/_next/static/chunks/$file" | grep -q "200 OK"; then
        ((SUCCESS_COUNT++))
    fi
done

if curl -s -I "https://horseoud.com/favicon.ico" | grep -q -E "(200 OK|404)"; then
    ((SUCCESS_COUNT++))
fi

echo "Successful: $SUCCESS_COUNT/$TOTAL_COUNT"

if [ $SUCCESS_COUNT -eq $TOTAL_COUNT ]; then
    echo "🎉 ALL TESTS PASSED! Static assets are working!"
    echo ""
    echo "✅ Next steps:"
    echo "1. Check your website - it should now load properly"
    echo "2. No more 502 errors in browser console"
    echo "3. CSS styling should be working"
    echo "4. JavaScript functionality restored"
else
    echo "❌ Some tests still failing. Check nginx configuration."
    echo ""
    echo "🔧 Troubleshooting:"
    echo "1. Verify nginx config was updated correctly"
    echo "2. Check: sudo nginx -t"
    echo "3. Reload: sudo systemctl reload nginx"
    echo "4. Check error log: sudo tail -f /var/log/nginx/error.log"
fi

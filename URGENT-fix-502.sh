#!/bin/bash

echo "🚨 URGENT: Fixing 502 Static Asset Errors"
echo "========================================="

# Variables
APP_PATH="/www/wwwroot/node/ecommerc"
STANDALONE_PATH="$APP_PATH/.next/standalone"
NGINX_CONF="/etc/nginx/sites-available/horseoud"  # Adjust this path

echo "🔍 Step 1: Diagnosing the problem..."

# Check if files exist but are not being served
echo "📁 Checking if static files exist..."

if [ -f "$STANDALONE_PATH/.next/static/chunks/2619-04bc32f026a0d946.js" ]; then
    echo "✅ Static file EXISTS: 2619-04bc32f026a0d946.js"
    echo "   Size: $(ls -lh $STANDALONE_PATH/.next/static/chunks/2619-04bc32f026a0d946.js | awk '{print $5}')"
else
    echo "❌ Static file MISSING: 2619-04bc32f026a0d946.js"
    echo "📋 Available chunks:"
    ls "$STANDALONE_PATH/.next/static/chunks/" | head -5
fi

if [ -f "$STANDALONE_PATH/.next/static/css/5bac398b91f1b183.css" ]; then
    echo "✅ CSS file EXISTS: 5bac398b91f1b183.css"
else
    echo "❌ CSS file MISSING: 5bac398b91f1b183.css"
    echo "📋 Available CSS:"
    find "$STANDALONE_PATH/.next/static" -name "*.css" | head -3
fi

echo ""
echo "🔧 Step 2: The Fix"
echo "=================="

echo "Your nginx is PROXYING static files instead of serving them directly!"
echo ""
echo "💡 IMMEDIATE ACTIONS REQUIRED:"
echo ""

echo "1. 📝 UPDATE your nginx configuration:"
echo "   sudo nano /etc/nginx/sites-available/horseoud"
echo ""
echo "2. 🔄 REPLACE your server block with the config from: URGENT-nginx-fix.conf"
echo ""
echo "3. ✅ TEST the configuration:"
echo "   sudo nginx -t"
echo ""
echo "4. 🚀 RELOAD nginx:"
echo "   sudo systemctl reload nginx"
echo ""
echo "5. 🧪 TEST a static file directly:"
echo "   curl -I https://horseoud.com/_next/static/chunks/main-app-a1f1f5d698df6a05.js"
echo "   Should return: 200 OK, not 502"
echo ""

echo "🎯 Root Cause:"
echo "Your nginx config is proxying ALL requests (including static files) to Node.js"
echo "Static files should be served DIRECTLY from the filesystem, not proxied!"
echo ""

echo "📊 Current Status:"
echo "✅ HTML pages: Working (200)"
echo "❌ Static assets: All failing (502)"
echo "🎯 Fix: Serve static files directly in nginx"

echo ""
echo "⚡ Quick Test Commands:"
echo "====================="

echo "# Test if files are accessible on filesystem:"
echo "ls -la $STANDALONE_PATH/.next/static/chunks/ | head -5"
echo ""
echo "# Test nginx config syntax:"
echo "sudo nginx -t"
echo ""
echo "# View current nginx config:"
echo "sudo cat /etc/nginx/sites-available/horseoud"
echo ""
echo "# Reload nginx after config change:"
echo "sudo systemctl reload nginx"

echo ""
echo "🆘 IF STILL BROKEN:"
echo "=================="
echo "1. Check nginx error log:"
echo "   sudo tail -f /var/log/nginx/error.log"
echo ""
echo "2. Verify app is running:"
echo "   ps aux | grep node"
echo ""
echo "3. Check app port:"
echo "   netstat -tulpn | grep :3000"

echo ""
echo "✅ SUCCESS INDICATORS:"
echo "===================="
echo "After the fix, you should see:"
echo "• No more 502 errors in browser console"
echo "• CSS loads and pages look styled"
echo "• JavaScript works properly"
echo "• Static files return 200 status codes"

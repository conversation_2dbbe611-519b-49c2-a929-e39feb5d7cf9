#!/usr/bin/env node

/**
 * Cache Clearing Script for Woo2 E-commerce
 * This script provides multiple methods to clear all caching layers
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Woo2 Cache Clearing Script');
console.log('================================');

// Function to remove directory recursively
function removeDir(dirPath) {
  if (fs.existsSync(dirPath)) {
    console.log(`🗑️  Removing: ${dirPath}`);
    try {
      fs.rmSync(dirPath, { recursive: true, force: true });
      console.log(`✅ Removed: ${dirPath}`);
    } catch (error) {
      console.log(`❌ Failed to remove ${dirPath}:`, error.message);
    }
  } else {
    console.log(`ℹ️  Directory doesn't exist: ${dirPath}`);
  }
}

// Clear Next.js cache
console.log('\n📁 Clearing Next.js cache...');
removeDir('.next');

// Clear node_modules cache
console.log('\n📦 Clearing Node.js cache...');
try {
  execSync('npm cache clean --force', { stdio: 'inherit' });
  console.log('✅ NPM cache cleared');
} catch (error) {
  console.log('ℹ️  NPM cache clean skipped');
}

// Clear build files
console.log('\n🏗️  Clearing build artifacts...');
removeDir('dist');
removeDir('build');

// Instructions for browser cache
console.log('\n🌐 Browser Cache Clearing Instructions:');
console.log('========================================');
console.log('1. Chrome/Edge: Ctrl+Shift+Delete → Clear browsing data → Cached images and files');
console.log('2. Firefox: Ctrl+Shift+Delete → Cache');
console.log('3. Safari: Develop → Empty Caches');
console.log('4. Or use: Ctrl+F5 (hard refresh)');
console.log('');

console.log('🔄 Next Steps:');
console.log('==============');
console.log('1. Restart your development server: npm run dev');
console.log('2. Hard refresh your browser: Ctrl+F5');
console.log('3. Test product updates in admin');
console.log('');

console.log('✅ Cache clearing completed!');
console.log('🎯 Your app should now show fresh data immediately.');

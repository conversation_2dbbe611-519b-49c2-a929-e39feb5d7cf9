# Correct Nginx Configuration for Proxy + Localhost Setup
# This serves static assets directly while proxying dynamic requests

server {
    listen 80;
    listen [::]:80;
    server_name horseoud.com www.horseoud.com;
    
    # Important: Set root to your standalone build directory
    root /www/wwwroot/node/ecommerc/.next/standalone;
    
    # SERVE STATIC ASSETS DIRECTLY (Don't proxy these!)
    # This is the key fix for your 502 errors
    
    # Serve Next.js built static files directly
    location /_next/static/ {
        alias /www/wwwroot/node/ecommerc/.next/standalone/.next/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "*";
        try_files $uri =404;
    }
    
    # Serve public assets (uploads, images) directly  
    location /uploads/ {
        alias /www/wwwroot/node/ecommerc/.next/standalone/public/uploads/;
        expires 1d;
        add_header Cache-Control "public";
        try_files $uri =404;
    }
    
    # Serve favicon directly
    location = /favicon.ico {
        alias /www/wwwroot/node/ecommerc/.next/standalone/public/favicon.ico;
        expires 1d;
        add_header Cache-Control "public";
        log_not_found off;
        access_log off;
    }
    
    # Serve other static files directly
    location ~* \.(ico|css|js|gif|jpeg|jpg|png|webp|svg|woff|woff2|ttf|eot)$ {
        root /www/wwwroot/node/ecommerc/.next/standalone/public;
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
        access_log off;
    }
    
    # PROXY EVERYTHING ELSE to your localhost Node.js app
    location / {
        # Your localhost app (adjust port if different)
        proxy_pass http://127.0.0.1:3000;
        
        # Standard proxy headers
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}

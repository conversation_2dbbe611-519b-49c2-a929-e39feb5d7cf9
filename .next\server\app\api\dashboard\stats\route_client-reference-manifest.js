globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/api/dashboard/stats/route"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./components/DynamicFavicon.tsx":{"*":{"id":"(ssr)/./components/DynamicFavicon.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/MobileNavigation.tsx":{"*":{"id":"(ssr)/./components/MobileNavigation.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ThemeProvider.tsx":{"*":{"id":"(ssr)/./components/ThemeProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./contexts/CartProviderWithReservations.tsx":{"*":{"id":"(ssr)/./contexts/CartProviderWithReservations.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./contexts/CurrencyContext.tsx":{"*":{"id":"(ssr)/./contexts/CurrencyContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./contexts/FavoritesContext.tsx":{"*":{"id":"(ssr)/./contexts/FavoritesContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./contexts/InventoryReservationContext.tsx":{"*":{"id":"(ssr)/./contexts/InventoryReservationContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./contexts/LanguageContext.tsx":{"*":{"id":"(ssr)/./contexts/LanguageContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/sonner/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/sonner/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/CategoriesSection.tsx":{"*":{"id":"(ssr)/./components/CategoriesSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/FollowUs.tsx":{"*":{"id":"(ssr)/./components/FollowUs.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/Footer.tsx":{"*":{"id":"(ssr)/./components/Footer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/Header.tsx":{"*":{"id":"(ssr)/./components/Header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/HeroSlider.tsx":{"*":{"id":"(ssr)/./components/HeroSlider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/HomeProductsSection.tsx":{"*":{"id":"(ssr)/./components/HomeProductsSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/HomeReviewsSection.tsx":{"*":{"id":"(ssr)/./components/HomeReviewsSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/WorkshopsSection.tsx":{"*":{"id":"(ssr)/./components/WorkshopsSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js":{"*":{"id":"(ssr)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js":{"*":{"id":"(ssr)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/layout.tsx":{"*":{"id":"(ssr)/./app/admin/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/page.tsx":{"*":{"id":"(ssr)/./app/admin/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/orders/page.tsx":{"*":{"id":"(ssr)/./app/admin/orders/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/products/page.tsx":{"*":{"id":"(ssr)/./app/admin/products/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/products/[id]/edit/page.tsx":{"*":{"id":"(ssr)/./app/admin/products/[id]/edit/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\node\\woo2\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\node\\woo2\\components\\DynamicFavicon.tsx":{"id":"(app-pages-browser)/./components/DynamicFavicon.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\node\\woo2\\components\\MobileNavigation.tsx":{"id":"(app-pages-browser)/./components/MobileNavigation.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\node\\woo2\\components\\ThemeProvider.tsx":{"id":"(app-pages-browser)/./components/ThemeProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\node\\woo2\\contexts\\CartProviderWithReservations.tsx":{"id":"(app-pages-browser)/./contexts/CartProviderWithReservations.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\node\\woo2\\contexts\\CurrencyContext.tsx":{"id":"(app-pages-browser)/./contexts/CurrencyContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\node\\woo2\\contexts\\FavoritesContext.tsx":{"id":"(app-pages-browser)/./contexts/FavoritesContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\node\\woo2\\contexts\\InventoryReservationContext.tsx":{"id":"(app-pages-browser)/./contexts/InventoryReservationContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\node\\woo2\\contexts\\LanguageContext.tsx":{"id":"(app-pages-browser)/./contexts/LanguageContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\node\\woo2\\node_modules\\sonner\\dist\\index.mjs":{"id":"(app-pages-browser)/./node_modules/sonner/dist/index.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\node\\woo2\\components\\CategoriesSection.tsx":{"id":"(app-pages-browser)/./components/CategoriesSection.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\node\\woo2\\components\\FollowUs.tsx":{"id":"(app-pages-browser)/./components/FollowUs.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\node\\woo2\\components\\Footer.tsx":{"id":"(app-pages-browser)/./components/Footer.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\node\\woo2\\components\\Header.tsx":{"id":"(app-pages-browser)/./components/Header.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\node\\woo2\\components\\HeroSlider.tsx":{"id":"(app-pages-browser)/./components/HeroSlider.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\node\\woo2\\components\\HomeProductsSection.tsx":{"id":"(app-pages-browser)/./components/HomeProductsSection.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\node\\woo2\\components\\HomeReviewsSection.tsx":{"id":"(app-pages-browser)/./components/HomeReviewsSection.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\node\\woo2\\components\\WorkshopsSection.tsx":{"id":"(app-pages-browser)/./components/WorkshopsSection.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\node\\woo2\\node_modules\\next\\dist\\client\\components\\builtin\\global-error.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\node\\woo2\\node_modules\\next\\dist\\esm\\client\\components\\builtin\\global-error.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\node\\woo2\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\node\\woo2\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\node\\woo2\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\node\\woo2\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\node\\woo2\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\node\\woo2\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\node\\woo2\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\node\\woo2\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\node\\woo2\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\node\\woo2\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\node\\woo2\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\node\\woo2\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\node\\woo2\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\node\\woo2\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\node\\woo2\\node_modules\\next\\dist\\lib\\metadata\\generate\\icon-mark.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\node\\woo2\\node_modules\\next\\dist\\esm\\lib\\metadata\\generate\\icon-mark.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\node\\woo2\\node_modules\\next\\dist\\next-devtools\\userspace\\app\\segment-explorer-node.js":{"id":"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\node\\woo2\\node_modules\\next\\dist\\esm\\next-devtools\\userspace\\app\\segment-explorer-node.js":{"id":"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\node\\woo2\\app\\admin\\layout.tsx":{"id":"(app-pages-browser)/./app/admin/layout.tsx","name":"*","chunks":[],"async":false},"D:\\node\\woo2\\app\\admin\\page.tsx":{"id":"(app-pages-browser)/./app/admin/page.tsx","name":"*","chunks":[],"async":false},"D:\\node\\woo2\\app\\admin\\orders\\page.tsx":{"id":"(app-pages-browser)/./app/admin/orders/page.tsx","name":"*","chunks":[],"async":false},"D:\\node\\woo2\\app\\admin\\products\\page.tsx":{"id":"(app-pages-browser)/./app/admin/products/page.tsx","name":"*","chunks":[],"async":false},"D:\\node\\woo2\\app\\admin\\products\\[id]\\edit\\page.tsx":{"id":"(app-pages-browser)/./app/admin/products/[id]/edit/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"D:\\node\\woo2\\":[],"D:\\node\\woo2\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"D:\\node\\woo2\\app\\page":[{"inlined":false,"path":"static/css/app/page.css"}],"D:\\node\\woo2\\app\\api\\dashboard\\stats\\route":[]},"rscModuleMapping":{"(app-pages-browser)/./app/globals.css":{"*":{"id":"(rsc)/./app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/DynamicFavicon.tsx":{"*":{"id":"(rsc)/./components/DynamicFavicon.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/MobileNavigation.tsx":{"*":{"id":"(rsc)/./components/MobileNavigation.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ThemeProvider.tsx":{"*":{"id":"(rsc)/./components/ThemeProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./contexts/CartProviderWithReservations.tsx":{"*":{"id":"(rsc)/./contexts/CartProviderWithReservations.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./contexts/CurrencyContext.tsx":{"*":{"id":"(rsc)/./contexts/CurrencyContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./contexts/FavoritesContext.tsx":{"*":{"id":"(rsc)/./contexts/FavoritesContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./contexts/InventoryReservationContext.tsx":{"*":{"id":"(rsc)/./contexts/InventoryReservationContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./contexts/LanguageContext.tsx":{"*":{"id":"(rsc)/./contexts/LanguageContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/sonner/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/sonner/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/CategoriesSection.tsx":{"*":{"id":"(rsc)/./components/CategoriesSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/FollowUs.tsx":{"*":{"id":"(rsc)/./components/FollowUs.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/Footer.tsx":{"*":{"id":"(rsc)/./components/Footer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/Header.tsx":{"*":{"id":"(rsc)/./components/Header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/HeroSlider.tsx":{"*":{"id":"(rsc)/./components/HeroSlider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/HomeProductsSection.tsx":{"*":{"id":"(rsc)/./components/HomeProductsSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/HomeReviewsSection.tsx":{"*":{"id":"(rsc)/./components/HomeReviewsSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/WorkshopsSection.tsx":{"*":{"id":"(rsc)/./components/WorkshopsSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js":{"*":{"id":"(rsc)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js":{"*":{"id":"(rsc)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/layout.tsx":{"*":{"id":"(rsc)/./app/admin/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/page.tsx":{"*":{"id":"(rsc)/./app/admin/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/orders/page.tsx":{"*":{"id":"(rsc)/./app/admin/orders/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/products/page.tsx":{"*":{"id":"(rsc)/./app/admin/products/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/products/[id]/edit/page.tsx":{"*":{"id":"(rsc)/./app/admin/products/[id]/edit/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}
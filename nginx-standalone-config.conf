# Nginx Configuration for Next.js Standalone Deployment
# Place this in your nginx server block

server {
    listen 80;
    server_name horseoud.com;
    
    # Root should point to the standalone directory
    root /www/wwwroot/node/ecommerc/.next/standalone;
    
    # Serve static files from public directory
    location /uploads/ {
        alias /www/wwwroot/node/ecommerc/.next/standalone/public/uploads/;
        expires 1d;
        add_header Cache-Control "public";
        access_log off;
    }
    
    # Serve Next.js static assets with long cache
    location /_next/static/ {
        alias /www/wwwroot/node/ecommerc/.next/standalone/.next/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
        
        # Handle CORS if needed
        add_header Access-Control-Allow-Origin "*";
    }
    
    # Serve other public assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|webp)$ {
        root /www/wwwroot/node/ecommerc/.next/standalone/public;
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }
    
    # Proxy all other requests to Next.js server
    location / {
        proxy_pass http://localhost:3000;  # or your app port
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}

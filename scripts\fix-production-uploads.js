#!/usr/bin/env node

/**
 * Production Upload Fix Script
 * Fixes image upload path issues in standalone Next.js deployment
 */

const fs = require('fs').promises
const path = require('path')

const COLORS = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function log(color, symbol, message) {
  console.log(`${color}${symbol} ${message}${COLORS.reset}`)
}

function logSuccess(message) { log(COLORS.green, '✅', message) }
function logError(message) { log(COLORS.red, '❌', message) }
function logWarning(message) { log(COLORS.yellow, '⚠️ ', message) }
function logInfo(message) { log(COLORS.blue, 'ℹ️ ', message) }
function logStep(message) { log(COLORS.cyan, '🔧', message) }

async function checkExists(filePath) {
  try {
    await fs.access(filePath)
    return true
  } catch {
    return false
  }
}

async function copyDirectory(source, destination) {
  try {
    if (!(await checkExists(source))) {
      logWarning(`Source directory doesn't exist: ${source}`)
      return false
    }

    // Create destination directory
    await fs.mkdir(destination, { recursive: true })

    // Get list of files in source
    const files = await fs.readdir(source, { withFileTypes: true })

    for (const file of files) {
      const sourcePath = path.join(source, file.name)
      const destPath = path.join(destination, file.name)

      if (file.isDirectory()) {
        await copyDirectory(sourcePath, destPath)
      } else {
        await fs.copyFile(sourcePath, destPath)
        logInfo(`Copied: ${file.name}`)
      }
    }

    logSuccess(`Successfully copied ${source} to ${destination}`)
    return true
  } catch (error) {
    logError(`Failed to copy directory: ${error.message}`)
    return false
  }
}

async function main() {
  console.log(`${COLORS.bright}${COLORS.magenta}🖼️ Production Upload Path Fix${COLORS.reset}`)
  console.log('='.repeat(35))

  // Define paths
  const paths = {
    currentUploads: 'public/uploads',
    standalonePublic: '.next/standalone/public',
    standaloneUploads: '.next/standalone/public/uploads'
  }

  logInfo('Checking current directory structure...')

  // Check if standalone build exists
  if (!(await checkExists('.next/standalone'))) {
    logError('Standalone build not found. Please run `npm run build` first.')
    process.exit(1)
  }

  // Check if current uploads exist
  if (!(await checkExists(paths.currentUploads))) {
    logWarning('No existing uploads found in public/uploads/')
  } else {
    logSuccess('Found existing uploads in public/uploads/')
  }

  console.log('\n📋 Step 1: Creating Standalone Upload Directories')

  // Create standalone upload directories
  const uploadDirs = [
    paths.standalonePublic,
    paths.standaloneUploads,
    path.join(paths.standaloneUploads, 'products'),
    path.join(paths.standaloneUploads, 'categories'),
    path.join(paths.standaloneUploads, 'branding')
  ]

  for (const dir of uploadDirs) {
    try {
      await fs.mkdir(dir, { recursive: true })
      logSuccess(`Created: ${dir}`)
    } catch (error) {
      if (error.code !== 'EEXIST') {
        logError(`Failed to create ${dir}: ${error.message}`)
      }
    }
  }

  console.log('\n📋 Step 2: Copying Existing Uploads')

  // Copy existing uploads to standalone directory
  if (await checkExists(paths.currentUploads)) {
    await copyDirectory(paths.currentUploads, paths.standaloneUploads)
  }

  console.log('\n🔒 Step 3: Setting Permissions')

  // Set proper permissions
  try {
    const { execSync } = require('child_process')
    execSync(`chmod -R 755 ${paths.standalonePublic}`)
    logSuccess('Set permissions for standalone public directory')
  } catch (error) {
    logWarning(`Could not set permissions automatically: ${error.message}`)
  }

  console.log('\n📝 Step 4: Verifying Fix')

  // Verify directories exist
  const checks = [
    { path: paths.standalonePublic, description: 'Standalone public directory' },
    { path: paths.standaloneUploads, description: 'Standalone uploads directory' },
    { path: path.join(paths.standaloneUploads, 'products'), description: 'Standalone products directory' }
  ]

  let allGood = true
  for (const check of checks) {
    if (await checkExists(check.path)) {
      logSuccess(`${check.description} exists`)
    } else {
      logError(`${check.description} missing`)
      allGood = false
    }
  }

  console.log('\n📄 Step 5: Creating Nginx Configuration')

  // Create corrected nginx configuration
  const nginxConfig = `# Corrected Nginx Configuration for Uploads
# Replace your current /uploads/ location block with this:

location /uploads/ {
    alias /www/wwwroot/node/ecommerc/.next/standalone/public/uploads/;
    expires 1d;
    add_header Cache-Control "public";
    try_files $uri =404;
}

# Alternative: If you want to serve from the main public directory instead:
# location /uploads/ {
#     alias /www/wwwroot/node/ecommerc/public/uploads/;
#     expires 1d;
#     add_header Cache-Control "public";
#     try_files $uri =404;
# }
`

  try {
    await fs.writeFile('nginx-uploads-fix.conf', nginxConfig)
    logSuccess('Created nginx-uploads-fix.conf with corrected configuration')
  } catch (error) {
    logWarning('Could not create nginx config file')
  }

  console.log('\n🚀 Next Steps:')

  if (allGood) {
    logSuccess('✅ Upload directories are properly set up!')
    console.log('')
    console.log('1. Restart your application:')
    console.log('   pm2 restart horseoud')
    console.log('')
    console.log('2. Reload nginx:')
    console.log('   sudo nginx -t && sudo systemctl reload nginx')
    console.log('')
    console.log('3. Test upload functionality in admin panel')
  } else {
    logError('❌ Some directories are still missing')
    console.log('')
    console.log('Manual fix commands:')
    console.log(`mkdir -p ${paths.standaloneUploads}/products`)
    console.log(`mkdir -p ${paths.standaloneUploads}/categories`)
    console.log(`cp -r public/uploads/* ${paths.standaloneUploads}/ 2>/dev/null || true`)
    console.log(`chmod -R 755 ${paths.standalonePublic}`)
  }

  console.log('')
  logInfo('The issue was: Files saved to wrong location vs nginx expecting different location')
  logInfo('Now uploads should work correctly in production!')
}

main().catch(error => {
  console.error('Fix script failed:', error)
  process.exit(1)
})

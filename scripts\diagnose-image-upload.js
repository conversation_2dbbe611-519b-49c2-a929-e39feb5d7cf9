#!/usr/bin/env node

/**
 * Production Image Upload Diagnostic Script
 * 
 * This script diagnoses image upload issues in production environment
 * specifically for standalone Next.js deployments.
 */

const fs = require('fs').promises
const path = require('path')

const COLORS = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function log(color, symbol, message) {
  console.log(`${color}${symbol} ${message}${COLORS.reset}`)
}

function logSuccess(message) { log(COLORS.green, '✅', message) }
function logError(message) { log(COLORS.red, '❌', message) }
function logWarning(message) { log(COLORS.yellow, '⚠️ ', message) }
function logInfo(message) { log(COLORS.blue, 'ℹ️ ', message) }
function logStep(message) { log(COLORS.cyan, '🔍', message) }

async function checkFileExists(filePath) {
  try {
    await fs.access(filePath)
    return true
  } catch {
    return false
  }
}

async function checkDirectoryPermissions(dirPath) {
  try {
    // Try to create a test file
    const testFile = path.join(dirPath, '.test-upload-permissions')
    await fs.writeFile(testFile, 'test')
    await fs.unlink(testFile)
    return true
  } catch (error) {
    return false
  }
}

async function getDirectoryInfo(dirPath) {
  try {
    const stats = await fs.stat(dirPath)
    const files = await fs.readdir(dirPath)
    return {
      exists: true,
      isDirectory: stats.isDirectory(),
      permissions: stats.mode.toString(8),
      fileCount: files.length,
      files: files.slice(0, 5) // Show first 5 files
    }
  } catch (error) {
    return {
      exists: false,
      error: error.message
    }
  }
}

async function main() {
  console.log(`${COLORS.bright}${COLORS.magenta}🔧 Production Image Upload Diagnostic${COLORS.reset}`)
  console.log('='.repeat(50))
  
  // Determine if we're in standalone build or regular build
  const isStandalone = await checkFileExists('.next/standalone/server.js')
  const currentDir = process.cwd()
  
  logInfo(`Current directory: ${currentDir}`)
  logInfo(`Standalone build: ${isStandalone ? 'Yes' : 'No'}`)
  
  // Define paths based on build type
  const paths = {
    public: isStandalone ? '.next/standalone/public' : 'public',
    uploads: isStandalone ? '.next/standalone/public/uploads' : 'public/uploads',
    productsUploads: isStandalone ? '.next/standalone/public/uploads/products' : 'public/uploads/products',
    categoriesUploads: isStandalone ? '.next/standalone/public/uploads/categories' : 'public/uploads/categories',
    nextStatic: isStandalone ? '.next/standalone/.next/static' : '.next/static'
  }
  
  console.log('\n📁 Checking Directory Structure:')
  
  // Check each critical directory
  for (const [name, dirPath] of Object.entries(paths)) {
    logStep(`Checking ${name}: ${dirPath}`)
    const info = await getDirectoryInfo(dirPath)
    
    if (info.exists) {
      logSuccess(`Directory exists (${info.fileCount} files, permissions: ${info.permissions})`)
      if (info.files.length > 0) {
        console.log(`   Sample files: ${info.files.join(', ')}`)
      }
      
      // Check write permissions
      if (name.includes('uploads')) {
        const canWrite = await checkDirectoryPermissions(dirPath)
        if (canWrite) {
          logSuccess('Write permissions: OK')
        } else {
          logError('Write permissions: FAILED')
        }
      }
    } else {
      logError(`Directory missing: ${info.error}`)
    }
  }
  
  console.log('\n🔧 Checking Image Processing Dependencies:')
  
  // Check if Sharp is available
  try {
    const sharp = require('sharp')
    logSuccess('Sharp library: Available')
    
    // Test Sharp functionality
    const testBuffer = Buffer.from('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7', 'base64')
    await sharp(testBuffer).webp().toBuffer()
    logSuccess('Sharp processing: Working')
  } catch (error) {
    logError(`Sharp library: ${error.message}`)
  }
  
  console.log('\n📡 Checking API Upload Endpoint:')
  
  // Check if upload API file exists
  const uploadApiPath = 'app/api/upload/route.ts'
  if (await checkFileExists(uploadApiPath)) {
    logSuccess('Upload API endpoint: Found')
  } else {
    logError('Upload API endpoint: Missing')
  }
  
  console.log('\n🖼️ Checking Image Upload Configuration:')
  
  // Check image-upload.ts
  const imageUploadLibPath = 'lib/image-upload.ts'
  if (await checkFileExists(imageUploadLibPath)) {
    logSuccess('Image upload library: Found')
    
    // Read and check the upload path configuration
    try {
      const content = await fs.readFile(imageUploadLibPath, 'utf8')
      if (content.includes('process.cwd()')) {
        logSuccess('Upload path: Uses process.cwd() (correct)')
      } else {
        logWarning('Upload path: May not use process.cwd()')
      }
    } catch (error) {
      logWarning(`Could not analyze upload library: ${error.message}`)
    }
  } else {
    logError('Image upload library: Missing')
  }
  
  console.log('\n🔍 Environment Analysis:')
  logInfo(`NODE_ENV: ${process.env.NODE_ENV || 'not set'}`)
  logInfo(`Process CWD: ${process.cwd()}`)
  
  console.log('\n💡 Diagnosis Summary:')
  
  if (isStandalone) {
    logInfo('You are using standalone Next.js build')
    logInfo('Upload path should be: .next/standalone/public/uploads/')
    
    const standaloneUploadsExists = await checkFileExists('.next/standalone/public/uploads')
    if (!standaloneUploadsExists) {
      logError('ISSUE FOUND: Uploads directory missing in standalone build')
      console.log('\n🛠️ RECOMMENDED FIXES:')
      console.log('1. Create missing upload directories')
      console.log('2. Copy existing uploads to standalone directory')
      console.log('3. Update nginx configuration')
      console.log('4. Restart application')
    }
  } else {
    logInfo('You are using regular Next.js build')
    logInfo('Upload path should be: public/uploads/')
  }
  
  console.log('\n🚀 Next Steps:')
  console.log('1. Run the fix script: node scripts/fix-image-upload-production.js')
  console.log('2. Test upload functionality')
  console.log('3. Check nginx configuration')
  console.log('4. Verify uploaded images are accessible')
}

main().catch(error => {
  console.error('Diagnostic script failed:', error)
  process.exit(1)
})

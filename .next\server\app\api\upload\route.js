/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/upload/route";
exports.ids = ["app/api/upload/route"];
exports.modules = {

/***/ "(rsc)/./app/api/upload/route.ts":
/*!*********************************!*\
  !*** ./app/api/upload/route.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_image_upload__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/image-upload */ \"(rsc)/./lib/image-upload.ts\");\n\n\nasync function POST(request) {\n    try {\n        console.log('📤 Upload request received');\n        const formData = await request.formData();\n        const files = formData.getAll('files');\n        const type = formData.get('type') || 'product';\n        console.log(`📁 Upload type: ${type}`);\n        console.log(`📎 Files received: ${files.length}`);\n        if (!files || files.length === 0) {\n            console.log('❌ No files provided');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'لم يتم تحديد ملفات للرفع'\n            }, {\n                status: 400\n            });\n        }\n        // Convert Files to buffer format for processing\n        console.log('🔄 Converting files to buffers...');\n        const fileBuffers = await Promise.all(files.map(async (file, index)=>{\n            console.log(`📄 Processing file ${index + 1}: ${file.name} (${file.size} bytes, ${file.type})`);\n            try {\n                const buffer = Buffer.from(await file.arrayBuffer());\n                console.log(`✅ File ${index + 1} converted to buffer (${buffer.length} bytes)`);\n                return {\n                    buffer,\n                    originalname: file.name,\n                    mimetype: file.type,\n                    size: file.size\n                };\n            } catch (error) {\n                console.error(`❌ Error converting file ${index + 1}:`, error);\n                throw new Error(`فشل في معالجة الملف ${file.name}`);\n            }\n        }));\n        // Validate all files first\n        console.log('🔍 Validating files...');\n        for (const [index, file] of fileBuffers.entries()){\n            console.log(`🔎 Validating file ${index + 1}: ${file.originalname}`);\n            const validation = (0,_lib_image_upload__WEBPACK_IMPORTED_MODULE_1__.validateImage)(file);\n            if (!validation.valid) {\n                console.log(`❌ Validation failed for ${file.originalname}: ${validation.error}`);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: validation.error\n                }, {\n                    status: 400\n                });\n            }\n            console.log(`✅ File ${index + 1} validation passed`);\n        }\n        // Process and optimize images\n        console.log('⚙️  Processing images...');\n        try {\n            const processedImages = await (0,_lib_image_upload__WEBPACK_IMPORTED_MODULE_1__.processMultipleImages)(fileBuffers, type);\n            console.log(`✅ Successfully processed ${processedImages.length} images`);\n            const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                images: processedImages,\n                message: `تم رفع ${processedImages.length} صورة بنجاح`\n            });\n            // Add minimal cache headers for successful uploads\n            response.headers.set('Cache-Control', 'no-cache');\n            response.headers.set('X-Upload-Success', 'true');\n            return response;\n        } catch (processError) {\n            console.error('❌ Image processing error:', processError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: processError instanceof Error ? processError.message : 'فشل في معالجة الصور'\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error('📤 Upload error:', error);\n        // Provide more detailed error information\n        let errorMessage = 'فشل في رفع الصور';\n        let errorDetails = '';\n        if (error instanceof Error) {\n            errorMessage = error.message;\n            errorDetails = error.stack || '';\n        }\n        // Log additional context for debugging\n        console.error('📋 Error details:', {\n            message: errorMessage,\n            details: errorDetails,\n            timestamp: new Date().toISOString(),\n            nodeVersion: process.version,\n            platform: process.platform\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: errorMessage,\n            details:  true ? errorDetails : 0,\n            timestamp: new Date().toISOString()\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE endpoint to remove images\nasync function DELETE(request) {\n    try {\n        const { imageUrl } = await request.json();\n        if (!imageUrl) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'لم يتم تحديد الصورة للحذف'\n            }, {\n                status: 400\n            });\n        }\n        await (0,_lib_image_upload__WEBPACK_IMPORTED_MODULE_1__.deleteImage)(imageUrl);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'تم حذف الصورة بنجاح'\n        });\n    } catch (error) {\n        console.error('Delete error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'فشل في حذف الصورة'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/upload/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/image-upload.ts":
/*!*****************************!*\
  !*** ./lib/image-upload.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   base64ToBuffer: () => (/* binding */ base64ToBuffer),\n/* harmony export */   deleteImage: () => (/* binding */ deleteImage),\n/* harmony export */   deleteMultipleImages: () => (/* binding */ deleteMultipleImages),\n/* harmony export */   getUploadStats: () => (/* binding */ getUploadStats),\n/* harmony export */   processBase64Image: () => (/* binding */ processBase64Image),\n/* harmony export */   processImage: () => (/* binding */ processImage),\n/* harmony export */   processMultipleImages: () => (/* binding */ processMultipleImages),\n/* harmony export */   validateImage: () => (/* binding */ validateImage)\n/* harmony export */ });\n/* harmony import */ var sharp__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! sharp */ \"sharp\");\n/* harmony import */ var sharp__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(sharp__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs_promises__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\n// Image optimization settings\nconst IMAGE_CONFIG = {\n    // Maximum dimensions\n    product: {\n        maxWidth: 1200,\n        maxHeight: 1200,\n        quality: 85,\n        format: 'webp',\n        fallbackFormat: 'jpeg'\n    },\n    thumbnail: {\n        maxWidth: 400,\n        maxHeight: 400,\n        quality: 80,\n        format: 'webp',\n        fallbackFormat: 'jpeg'\n    },\n    category: {\n        maxWidth: 800,\n        maxHeight: 600,\n        quality: 85,\n        format: 'webp',\n        fallbackFormat: 'jpeg'\n    },\n    logo: {\n        maxWidth: 300,\n        maxHeight: 100,\n        quality: 90,\n        format: 'png',\n        fallbackFormat: 'png'\n    },\n    favicon: {\n        maxWidth: 64,\n        maxHeight: 64,\n        quality: 95,\n        format: 'png',\n        fallbackFormat: 'png'\n    }\n};\n// Allowed image types\nconst ALLOWED_TYPES = [\n    'image/jpeg',\n    'image/jpg',\n    'image/png',\n    'image/webp',\n    'image/gif'\n];\nconst MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB\n;\n/**\n * Validate image file\n */ function validateImage(file) {\n    if (!file) {\n        return {\n            valid: false,\n            error: 'لم يتم تحديد ملف'\n        };\n    }\n    if (!ALLOWED_TYPES.includes(file.mimetype)) {\n        return {\n            valid: false,\n            error: 'نوع الملف غير مسموح. الأنواع المسموحة: JPG, PNG, WebP, GIF'\n        };\n    }\n    if (file.size > MAX_FILE_SIZE) {\n        return {\n            valid: false,\n            error: `حجم الملف كبير جداً. الحد الأقصى ${MAX_FILE_SIZE / 1024 / 1024}MB`\n        };\n    }\n    return {\n        valid: true\n    };\n}\n/**\n * Generate unique filename\n */ function generateFileName(originalName, format = 'webp') {\n    const timestamp = Date.now();\n    const random = crypto__WEBPACK_IMPORTED_MODULE_3___default().randomBytes(8).toString('hex');\n    const ext = format.toLowerCase();\n    const nameWithoutExt = path__WEBPACK_IMPORTED_MODULE_1___default().parse(originalName).name.toLowerCase().replace(/[^a-z0-9]/g, '-').slice(0, 50) // Limit filename length\n    ;\n    return `${timestamp}-${random}-${nameWithoutExt}.${ext}`;\n}\n/**\n * Get the correct base path for the application\n * Handles both regular builds and standalone builds\n */ function getBasePath() {\n    const cwd = process.cwd();\n    // Check if we're in a standalone build\n    // Standalone builds have server.js in the current directory\n    try {\n        const serverJsPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(cwd, 'server.js');\n        const standaloneServerExists = (__webpack_require__(/*! fs */ \"fs\").existsSync)(serverJsPath);\n        if (standaloneServerExists) {\n            console.log('🔍 Detected standalone Next.js build');\n            return cwd // Already in standalone directory\n            ;\n        } else {\n            console.log('🔍 Detected regular Next.js build');\n            return cwd;\n        }\n    } catch (error) {\n        console.log('🔍 Using default build path');\n        return cwd;\n    }\n}\n/**\n * Ensure upload directory exists\n */ async function ensureUploadDir(uploadPath) {\n    try {\n        await fs_promises__WEBPACK_IMPORTED_MODULE_2___default().access(uploadPath);\n        console.log(`✅ Upload directory exists: ${uploadPath}`);\n    } catch  {\n        console.log(`📁 Creating upload directory: ${uploadPath}`);\n        try {\n            await fs_promises__WEBPACK_IMPORTED_MODULE_2___default().mkdir(uploadPath, {\n                recursive: true,\n                mode: 493\n            });\n            console.log(`✅ Upload directory created: ${uploadPath}`);\n        } catch (mkdirError) {\n            console.error(`❌ Failed to create upload directory: ${uploadPath}`, mkdirError);\n            throw new Error(`فشل في إنشاء مجلد الرفع: ${uploadPath}`);\n        }\n    }\n    // Verify write permissions\n    try {\n        const testFile = path__WEBPACK_IMPORTED_MODULE_1___default().join(uploadPath, '.test-write');\n        await fs_promises__WEBPACK_IMPORTED_MODULE_2___default().writeFile(testFile, 'test');\n        await fs_promises__WEBPACK_IMPORTED_MODULE_2___default().unlink(testFile);\n        console.log(`✅ Write permissions verified: ${uploadPath}`);\n    } catch (permError) {\n        console.error(`❌ No write permissions: ${uploadPath}`, permError);\n        // Try to fix permissions\n        try {\n            await fs_promises__WEBPACK_IMPORTED_MODULE_2___default().chmod(uploadPath, 493);\n            console.log(`🔧 Fixed permissions for: ${uploadPath}`);\n            // Test again\n            const testFile = path__WEBPACK_IMPORTED_MODULE_1___default().join(uploadPath, '.test-write');\n            await fs_promises__WEBPACK_IMPORTED_MODULE_2___default().writeFile(testFile, 'test');\n            await fs_promises__WEBPACK_IMPORTED_MODULE_2___default().unlink(testFile);\n            console.log(`✅ Write permissions verified after fix: ${uploadPath}`);\n        } catch (fixError) {\n            console.error(`❌ Could not fix permissions: ${uploadPath}`, fixError);\n            throw new Error(`لا توجد صلاحيات الكتابة في مجلد الرفع: ${uploadPath}`);\n        }\n    }\n}\n/**\n * Process and optimize image\n */ async function processImage(inputBuffer, type, originalName) {\n    const config = IMAGE_CONFIG[type] || IMAGE_CONFIG.product;\n    console.log(`🖼️  Processing image: ${originalName} (${inputBuffer.length} bytes) for type: ${type}`);\n    try {\n        // Get image metadata\n        console.log('📊 Getting image metadata...');\n        const metadata = await sharp__WEBPACK_IMPORTED_MODULE_0___default()(inputBuffer).metadata();\n        console.log(`✅ Image metadata: ${metadata.width}x${metadata.height}, ${metadata.format}`);\n        // Process main image\n        console.log('🔄 Processing image with Sharp...');\n        let processedImage = sharp__WEBPACK_IMPORTED_MODULE_0___default()(inputBuffer).resize(config.maxWidth, config.maxHeight, {\n            fit: 'inside',\n            withoutEnlargement: true\n        });\n        // Apply format based on type\n        if (config.format === 'png') {\n            processedImage = processedImage.png({\n                quality: config.quality\n            });\n        } else if (config.format === 'webp') {\n            processedImage = processedImage.webp({\n                quality: config.quality\n            });\n        } else {\n            processedImage = processedImage.jpeg({\n                quality: config.quality || 85\n            });\n        }\n        // Generate filename\n        const fileName = generateFileName(originalName, config.format);\n        const uploadDir = type === 'category' ? 'categories' : type === 'logo' || type === 'favicon' ? 'branding' : 'products';\n        // Use the correct base path for different build types\n        const basePath = getBasePath();\n        const uploadPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(basePath, 'public', 'uploads', uploadDir);\n        console.log(`📁 Upload path: ${uploadPath}`);\n        console.log(`📄 Generated filename: ${fileName}`);\n        // Ensure directory exists\n        await ensureUploadDir(uploadPath);\n        // Save processed image\n        const filePath = path__WEBPACK_IMPORTED_MODULE_1___default().join(uploadPath, fileName);\n        console.log(`💾 Saving to: ${filePath}`);\n        const outputInfo = await processedImage.toFile(filePath);\n        console.log(`✅ Image saved: ${outputInfo.size} bytes, ${outputInfo.width}x${outputInfo.height}`);\n        // Create relative URL for database\n        const url = `/uploads/${uploadDir}/${fileName}`;\n        console.log(`🔗 Generated URL: ${url}`);\n        return {\n            url,\n            width: outputInfo.width || config.maxWidth,\n            height: outputInfo.height || config.maxHeight,\n            size: outputInfo.size || 0,\n            format: outputInfo.format || config.format\n        };\n    } catch (error) {\n        console.error('❌ Error processing image:', error);\n        console.error('📋 Error details:', {\n            type,\n            originalName,\n            bufferSize: inputBuffer.length,\n            errorMessage: error instanceof Error ? error.message : String(error),\n            stack: error instanceof Error ? error.stack : undefined\n        });\n        throw new Error(`فشل في معالجة الصورة: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`);\n    }\n}\n/**\n * Process multiple images\n */ async function processMultipleImages(files, type = 'product') {\n    const processedImages = [];\n    for (const file of files){\n        // Validate each file\n        const validation = validateImage(file);\n        if (!validation.valid) {\n            throw new Error(validation.error);\n        }\n        // Process image\n        const processed = await processImage(file.buffer, type, file.originalname);\n        processedImages.push(processed);\n        // Also create thumbnail for products\n        if (type === 'product') {\n            const thumbnail = await processImage(file.buffer, 'thumbnail', file.originalname);\n            processedImages[processedImages.length - 1].thumbnailUrl = thumbnail.url;\n        }\n    }\n    return processedImages;\n}\n/**\n * Delete image file\n */ async function deleteImage(imageUrl) {\n    if (!imageUrl || !imageUrl.startsWith('/uploads/')) {\n        return; // Only delete our uploaded images\n    }\n    try {\n        const basePath = getBasePath();\n        const filePath = path__WEBPACK_IMPORTED_MODULE_1___default().join(basePath, 'public', imageUrl);\n        await fs_promises__WEBPACK_IMPORTED_MODULE_2___default().unlink(filePath);\n        console.log(`✅ Deleted image: ${filePath}`);\n    } catch (error) {\n        console.error('Error deleting image:', error);\n    // Don't throw error if file doesn't exist\n    }\n}\n/**\n * Delete multiple images\n */ async function deleteMultipleImages(imageUrls) {\n    await Promise.all(imageUrls.map((url)=>deleteImage(url)));\n}\n/**\n * Convert base64 to buffer\n */ function base64ToBuffer(base64String) {\n    // Remove data URL prefix if present\n    const base64Data = base64String.replace(/^data:image\\/\\w+;base64,/, '');\n    return Buffer.from(base64Data, 'base64');\n}\n/**\n * Process base64 image\n */ async function processBase64Image(base64String, type = 'product', filename = 'image') {\n    const buffer = base64ToBuffer(base64String);\n    return processImage(buffer, type, filename);\n}\n/**\n * Get upload statistics for monitoring\n */ async function getUploadStats() {\n    const basePath = getBasePath();\n    const uploadsPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(basePath, 'public', 'uploads');\n    let totalImages = 0;\n    let totalSize = 0;\n    let productImages = 0;\n    let categoryImages = 0;\n    try {\n        // Check products directory\n        const productsPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(uploadsPath, 'products');\n        try {\n            const productFiles = await fs_promises__WEBPACK_IMPORTED_MODULE_2___default().readdir(productsPath);\n            productImages = productFiles.length;\n            for (const file of productFiles){\n                const filePath = path__WEBPACK_IMPORTED_MODULE_1___default().join(productsPath, file);\n                const stats = await fs_promises__WEBPACK_IMPORTED_MODULE_2___default().stat(filePath);\n                totalSize += stats.size;\n            }\n        } catch (error) {\n            console.log('Products directory not accessible:', error instanceof Error ? error.message : String(error));\n        }\n        // Check categories directory\n        const categoriesPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(uploadsPath, 'categories');\n        try {\n            const categoryFiles = await fs_promises__WEBPACK_IMPORTED_MODULE_2___default().readdir(categoriesPath);\n            categoryImages = categoryFiles.length;\n            for (const file of categoryFiles){\n                const filePath = path__WEBPACK_IMPORTED_MODULE_1___default().join(categoriesPath, file);\n                const stats = await fs_promises__WEBPACK_IMPORTED_MODULE_2___default().stat(filePath);\n                totalSize += stats.size;\n            }\n        } catch (error) {\n            console.log('Categories directory not accessible:', error instanceof Error ? error.message : String(error));\n        }\n        totalImages = productImages + categoryImages;\n    } catch (error) {\n        console.error('Error getting upload stats:', error);\n    }\n    return {\n        totalImages,\n        totalSize,\n        productImages,\n        categoryImages,\n        basePath\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/image-upload.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fupload%2Froute&page=%2Fapi%2Fupload%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fupload%2Froute.ts&appDir=D%3A%5Cnode%5Cwoo2%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cnode%5Cwoo2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fupload%2Froute&page=%2Fapi%2Fupload%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fupload%2Froute.ts&appDir=D%3A%5Cnode%5Cwoo2%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cnode%5Cwoo2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var D_node_woo2_app_api_upload_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./app/api/upload/route.ts */ \"(rsc)/./app/api/upload/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/upload/route\",\n        pathname: \"/api/upload\",\n        filename: \"route\",\n        bundlePath: \"app/api/upload/route\"\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    resolvedPagePath: \"D:\\\\node\\\\woo2\\\\app\\\\api\\\\upload\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_node_woo2_app_api_upload_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/upload/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fupload%2Froute&page=%2Fapi%2Fupload%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fupload%2Froute.ts&appDir=D%3A%5Cnode%5Cwoo2%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cnode%5Cwoo2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("fs/promises");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "sharp":
/*!************************!*\
  !*** external "sharp" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("sharp");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fupload%2Froute&page=%2Fapi%2Fupload%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fupload%2Froute.ts&appDir=D%3A%5Cnode%5Cwoo2%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cnode%5Cwoo2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();
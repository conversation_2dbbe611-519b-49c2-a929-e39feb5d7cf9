#!/bin/bash

# Production Asset Fix Script
# Fixes 502 errors for static assets on production server

echo "🔧 Production Asset Fix Script"
echo "=============================="

# Check if we're on the production server
if [ ! -d ".next" ]; then
    echo "❌ .next directory not found. Please run this on the production server after build."
    exit 1
fi

echo "📁 Checking .next directory structure..."

# Check if static files exist
if [ -d ".next/static" ]; then
    echo "✅ .next/static directory exists"
    echo "📊 Static files count: $(find .next/static -type f | wc -l)"
else
    echo "❌ .next/static directory missing"
    exit 1
fi

# Check permissions
echo "🔒 Checking file permissions..."

# Fix permissions for .next directory
chmod -R 755 .next
echo "✅ Fixed .next directory permissions"

# Fix permissions for public directory
chmod -R 755 public
echo "✅ Fixed public directory permissions"

# Check if static files are readable
if [ -f ".next/static/chunks/webpack-*.js" ]; then
    echo "✅ Webpack chunks found"
else
    echo "⚠️  Webpack chunks not found - may need to rebuild"
fi

# Create nginx configuration snippet
echo "📝 Creating nginx configuration snippet..."

cat > nginx-static-assets.conf << 'EOF'
# Add this to your nginx server block to fix static asset serving

# Serve Next.js static assets with proper caching
location /_next/static/ {
    alias /path/to/your/app/.next/static/;
    expires 1y;
    add_header Cache-Control "public, immutable";
    access_log off;
    
    # Handle CORS if needed
    add_header Access-Control-Allow-Origin "*";
    add_header Access-Control-Allow-Methods "GET, OPTIONS";
    add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range";
}

# Serve uploaded images
location /uploads/ {
    alias /path/to/your/app/public/uploads/;
    expires 1d;
    add_header Cache-Control "public";
    access_log off;
}

# Serve other public assets
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|webp)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    access_log off;
}
EOF

echo "✅ Created nginx-static-assets.conf"

# Check Node.js and npm versions
echo "📦 Environment check..."
echo "Node.js version: $(node --version)"
echo "npm version: $(npm --version)"

# Check if PM2 is running the app
if command -v pm2 &> /dev/null; then
    echo "🔄 PM2 status:"
    pm2 list
else
    echo "⚠️  PM2 not found"
fi

# Recommendations
echo ""
echo "💡 Recommendations:"
echo "=================="
echo ""
echo "1. If using nginx, add the configuration from nginx-static-assets.conf"
echo "2. Restart your web server:"
echo "   sudo systemctl reload nginx"
echo ""
echo "3. If using PM2, restart the app:"
echo "   pm2 restart horseoud"
echo ""
echo "4. Clear any CDN cache (if using Cloudflare, etc.)"
echo ""
echo "5. Check your proxy configuration to ensure static assets are served correctly"
echo ""
echo "6. If still getting 502 errors, check server logs:"
echo "   sudo tail -f /var/log/nginx/error.log"
echo ""
echo "7. Verify the build completed successfully:"
echo "   npm run build"
echo ""

echo "✅ Production asset fix script completed!"

#!/bin/bash

echo "🔍 Production Server Diagnostic"
echo "==============================="

APP_PATH="/www/wwwroot/node/ecommerc"
STANDALONE_PATH="$APP_PATH/.next/standalone"

echo "📁 Checking directory structure..."

# Check if standalone build exists
if [ -d "$STANDALONE_PATH" ]; then
    echo "✅ Standalone build exists: $STANDALONE_PATH"
else
    echo "❌ Standalone build missing: $STANDALONE_PATH"
    exit 1
fi

# Check if public folder was copied
if [ -d "$STANDALONE_PATH/public" ]; then
    echo "✅ Public folder copied to standalone"
    echo "   📊 Uploads count: $(find $STANDALONE_PATH/public/uploads -name "*.webp" 2>/dev/null | wc -l)"
else
    echo "❌ Public folder missing in standalone"
fi

# Check if static assets exist
if [ -d "$STANDALONE_PATH/.next/static" ]; then
    echo "✅ Static assets exist"
    echo "   📊 JS chunks: $(find $STANDALONE_PATH/.next/static/chunks -name "*.js" 2>/dev/null | wc -l)"
    echo "   📊 CSS files: $(find $STANDALONE_PATH/.next/static -name "*.css" 2>/dev/null | wc -l)"
else
    echo "❌ Static assets missing"
fi

# Check permissions
echo ""
echo "🔒 Checking permissions..."
ls -la "$STANDALONE_PATH" | head -5

# Check if server.js exists and is executable
if [ -f "$STANDALONE_PATH/server.js" ]; then
    echo "✅ server.js exists"
else
    echo "❌ server.js missing"
fi

# Check specific problematic file
PROBLEM_CHUNK="$STANDALONE_PATH/.next/static/chunks/2619-04bc32f026a0d946.js"
if [ -f "$PROBLEM_CHUNK" ]; then
    echo "✅ Problematic chunk exists: $(ls -lh $PROBLEM_CHUNK)"
else
    echo "⚠️  Problematic chunk missing - might be renamed after rebuild"
    echo "📋 Available chunks:"
    ls "$STANDALONE_PATH/.next/static/chunks/" | head -5
fi

echo ""
echo "🌐 Testing file accessibility..."

# Test if files are readable
cd "$STANDALONE_PATH"
if [ -r "public/uploads/products" ]; then
    echo "✅ Uploads directory readable"
else
    echo "❌ Uploads directory not readable"
fi

echo ""
echo "💡 Next steps:"
echo "1. Verify nginx/apache configuration points to: $STANDALONE_PATH"
echo "2. Restart web server after config changes"
echo "3. Check web server error logs for specific 502 causes"

{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "gHBTJBDyk9UodIAzhXHMznICGVW/aACb93fVAYqsvJ4=", "__NEXT_PREVIEW_MODE_ID": "839ef531be35063767e6d6182983b51d", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "acbe31b94f80c36e1508214924a594c44fed1f372cd19c28eb51827cd0efa538", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "36c44e7e872b8a51cf8045b6e3201b0d5baeea16b3c8865fec6d6d292ef7f3d8"}}}, "functions": {}, "sortedMiddleware": ["/"]}
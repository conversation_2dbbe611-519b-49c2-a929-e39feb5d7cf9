#!/usr/bin/env node

/**
 * Production Image Upload Fix Script
 * 
 * This script fixes image upload issues in standalone Next.js production deployments
 * by creating proper upload directories and setting correct permissions.
 */

const fs = require('fs').promises
const path = require('path')
const { execSync } = require('child_process')

const COLORS = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function log(color, symbol, message) {
  console.log(`${color}${symbol} ${message}${COLORS.reset}`)
}

function logSuccess(message) { log(COLORS.green, '✅', message) }
function logError(message) { log(COLORS.red, '❌', message) }
function logWarning(message) { log(COLORS.yellow, '⚠️ ', message) }
function logInfo(message) { log(COLORS.blue, 'ℹ️ ', message) }
function logStep(message) { log(COLORS.cyan, '🔧', message) }

async function checkExists(filePath) {
  try {
    await fs.access(filePath)
    return true
  } catch {
    return false
  }
}

async function createDirectoryWithPermissions(dirPath, permissions = 0o755) {
  try {
    await fs.mkdir(dirPath, { recursive: true, mode: permissions })
    logSuccess(`Created directory: ${dirPath}`)
    return true
  } catch (error) {
    logError(`Failed to create directory ${dirPath}: ${error.message}`)
    return false
  }
}

async function copyDirectory(source, destination) {
  try {
    if (!(await checkExists(source))) {
      logWarning(`Source directory doesn't exist: ${source}`)
      return false
    }

    // Create destination directory
    await createDirectoryWithPermissions(destination)

    // Get list of files in source
    const files = await fs.readdir(source, { withFileTypes: true })

    for (const file of files) {
      const sourcePath = path.join(source, file.name)
      const destPath = path.join(destination, file.name)

      if (file.isDirectory()) {
        await copyDirectory(sourcePath, destPath)
      } else {
        await fs.copyFile(sourcePath, destPath)
        logInfo(`Copied: ${file.name}`)
      }
    }

    logSuccess(`Successfully copied ${source} to ${destination}`)
    return true
  } catch (error) {
    logError(`Failed to copy directory: ${error.message}`)
    return false
  }
}

async function setPermissions(dirPath, permissions = 0o755) {
  try {
    await fs.chmod(dirPath, permissions)
    
    // Also set permissions for all subdirectories and files
    const items = await fs.readdir(dirPath, { withFileTypes: true })
    
    for (const item of items) {
      const itemPath = path.join(dirPath, item.name)
      if (item.isDirectory()) {
        await setPermissions(itemPath, permissions)
      } else {
        await fs.chmod(itemPath, 0o644) // Files get read/write for owner, read for others
      }
    }
    
    logSuccess(`Set permissions for: ${dirPath}`)
    return true
  } catch (error) {
    logError(`Failed to set permissions for ${dirPath}: ${error.message}`)
    return false
  }
}

async function testUploadPermissions(dirPath) {
  try {
    const testFile = path.join(dirPath, '.test-upload-write')
    await fs.writeFile(testFile, 'test upload permissions')
    await fs.unlink(testFile)
    logSuccess(`Upload permissions test: PASSED for ${dirPath}`)
    return true
  } catch (error) {
    logError(`Upload permissions test: FAILED for ${dirPath} - ${error.message}`)
    return false
  }
}

async function main() {
  console.log(`${COLORS.bright}${COLORS.magenta}🛠️ Production Image Upload Fix${COLORS.reset}`)
  console.log('='.repeat(40))
  
  // Check if we're in standalone build
  const isStandalone = await checkExists('.next/standalone/server.js')
  
  if (!isStandalone) {
    logError('This script is designed for standalone Next.js builds')
    logError('Please run this on your production server with standalone build')
    process.exit(1)
  }
  
  logInfo('Detected standalone Next.js build')
  
  // Define paths
  const paths = {
    standaloneRoot: '.next/standalone',
    standalonePublic: '.next/standalone/public',
    standaloneUploads: '.next/standalone/public/uploads',
    standaloneProductsUploads: '.next/standalone/public/uploads/products',
    standaloneCategoriesUploads: '.next/standalone/public/uploads/categories',
    standaloneBrandingUploads: '.next/standalone/public/uploads/branding',
    originalPublic: 'public',
    originalUploads: 'public/uploads'
  }
  
  console.log('\n📁 Step 1: Creating Upload Directories')
  
  // Create all necessary upload directories
  const uploadDirs = [
    paths.standalonePublic,
    paths.standaloneUploads,
    paths.standaloneProductsUploads,
    paths.standaloneCategoriesUploads,
    paths.standaloneBrandingUploads
  ]
  
  for (const dir of uploadDirs) {
    logStep(`Creating directory: ${dir}`)
    await createDirectoryWithPermissions(dir, 0o755)
  }
  
  console.log('\n📋 Step 2: Copying Existing Uploads')
  
  // Copy existing uploads if they exist
  if (await checkExists(paths.originalUploads)) {
    logStep('Copying existing uploads to standalone directory')
    await copyDirectory(paths.originalUploads, paths.standaloneUploads)
  } else {
    logWarning('No existing uploads found to copy')
  }
  
  // Copy other public assets
  if (await checkExists(paths.originalPublic)) {
    logStep('Copying other public assets')
    const publicItems = await fs.readdir(paths.originalPublic, { withFileTypes: true })
    
    for (const item of publicItems) {
      if (item.name !== 'uploads') { // Skip uploads as we handled it separately
        const sourcePath = path.join(paths.originalPublic, item.name)
        const destPath = path.join(paths.standalonePublic, item.name)
        
        if (item.isDirectory()) {
          await copyDirectory(sourcePath, destPath)
        } else {
          try {
            await fs.copyFile(sourcePath, destPath)
            logInfo(`Copied: ${item.name}`)
          } catch (error) {
            logWarning(`Failed to copy ${item.name}: ${error.message}`)
          }
        }
      }
    }
  }
  
  console.log('\n🔒 Step 3: Setting Proper Permissions')
  
  // Set proper permissions for all upload directories
  for (const dir of uploadDirs) {
    if (await checkExists(dir)) {
      await setPermissions(dir, 0o755)
    }
  }
  
  console.log('\n🧪 Step 4: Testing Upload Functionality')
  
  // Test write permissions for each upload directory
  const testResults = []
  for (const dir of [paths.standaloneUploads, paths.standaloneProductsUploads, paths.standaloneCategoriesUploads]) {
    if (await checkExists(dir)) {
      const canWrite = await testUploadPermissions(dir)
      testResults.push({ dir, canWrite })
    }
  }
  
  console.log('\n📝 Step 5: Updating Configuration Files')
  
  // Check if nginx configuration needs updating
  logStep('Checking nginx configuration...')
  
  const nginxConfigPath = '/etc/nginx/sites-available/horseoud.com'
  const localNginxConfig = 'URGENT-nginx-fix.conf'
  
  if (await checkExists(localNginxConfig)) {
    logInfo('Found nginx configuration template')
    logWarning('Please update your nginx configuration with the template in URGENT-nginx-fix.conf')
    logWarning('Make sure the uploads location points to: .next/standalone/public/uploads/')
  }
  
  console.log('\n📊 Fix Summary:')
  
  // Display results
  const allTestsPassed = testResults.every(result => result.canWrite)
  
  if (allTestsPassed) {
    logSuccess('✅ All upload directories created and working!')
    logSuccess('✅ Permissions set correctly')
    logSuccess('✅ Upload functionality should now work')
  } else {
    logError('❌ Some upload directories have permission issues')
    testResults.forEach(result => {
      if (!result.canWrite) {
        logError(`Failed: ${result.dir}`)
      }
    })
  }
  
  console.log('\n🚀 Next Steps:')
  console.log('1. Restart your Next.js application:')
  console.log('   pm2 restart horseoud')
  console.log('')
  console.log('2. Update nginx configuration if needed:')
  console.log('   sudo nano /etc/nginx/sites-available/horseoud.com')
  console.log('   sudo nginx -t && sudo systemctl reload nginx')
  console.log('')
  console.log('3. Test image upload in admin panel')
  console.log('')
  console.log('4. Check uploaded images are accessible at:')
  console.log('   https://horseoud.com/uploads/products/')
  console.log('')
  
  if (allTestsPassed) {
    logSuccess('🎉 Image upload fix completed successfully!')
  } else {
    logError('⚠️ Some issues remain. Check permissions manually:')
    console.log('   chmod -R 755 .next/standalone/public/uploads')
    console.log('   chown -R www-data:www-data .next/standalone/public/uploads')
  }
}

main().catch(error => {
  console.error('Fix script failed:', error)
  process.exit(1)
})

# URGENT: Replace your nginx server block with this configuration
# This fixes the 502 errors for static assets

server {
    listen 80;
    listen [::]:80;
    server_name horseoud.com www.horseoud.com;
    
    # Set the root to your standalone build directory
    root /www/wwwroot/node/ecommerc/.next/standalone;
    
    # CRITICAL: Serve static assets directly from filesystem
    # DO NOT proxy these to Node.js!
    
    # Serve Next.js static chunks and CSS
    location /_next/static/ {
        alias /www/wwwroot/node/ecommerc/.next/standalone/.next/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "*";
        try_files $uri $uri/ =404;
    }
    
    # Serve public assets (uploads, images, favicon)
    location /uploads/ {
        alias /www/wwwroot/node/ecommerc/.next/standalone/public/uploads/;
        expires 1d;
        add_header Cache-Control "public";
        try_files $uri $uri/ =404;
    }
    
    # Serve favicon and other public assets
    location /favicon.ico {
        alias /www/wwwroot/node/ecommerc/.next/standalone/public/favicon.ico;
        expires 1d;
        add_header Cache-Control "public";
    }
    
    # Serve other public files
    location ~* \.(ico|css|js|gif|jpeg|jpg|png|webp|svg|woff|woff2|ttf|eot)$ {
        root /www/wwwroot/node/ecommerc/.next/standalone/public;
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }
    
    # Proxy ALL other requests to Next.js app
    location / {
        proxy_pass http://127.0.0.1:3000;  # or your app port
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }
}

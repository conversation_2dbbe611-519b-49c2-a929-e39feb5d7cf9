# 📚 Development Guide Index

This is the central index for all documentation files in the project. Each document covers specific aspects of the system.

## 🚨 Critical Documentation

### **Production Issues**
- [**production-server-diagnosis.md**](production-server-diagnosis.md) - Live server error analysis and recovery procedures
- [**product-api-security-fix.md**](product-api-security-fix.md) - Critical security fix for product API authentication
- [**critical-security-audit-2025.md**](critical-security-audit-2025.md) - 🚨 CRITICAL: Complete security audit and fixes

### **Security & Authentication**
- [**JWT_SECURITY_SETUP.md**](JWT_SECURITY_SETUP.md) - JWT token security configuration
- [**authentication-system.md**](authentication-system.md) - Complete authentication system overview
- [**secure-auth-migration.md**](secure-auth-migration.md) - Secure authentication migration guide
- [**secure-remember-me.md**](secure-remember-me.md) - Remember me functionality security
- [**secure-session-system.md**](secure-session-system.md) - Session management security

### **Environment & Setup**
- [**ENVIRONMENT_SETUP.md**](ENVIRONMENT_SETUP.md) - Required environment variables and configuration

## 🛠️ Feature Documentation

### **Payment Integration**
- [**thawani-integration-final.md**](thawani-integration-final.md) - Complete Thawani payment gateway integration
- [**thawani-development-testing.md**](thawani-development-testing.md) - Testing procedures for payment system

### **Product Management**
- [**product-type-database-fix.md**](product-type-database-fix.md) - Product type database schema fixes
- [**variable-product-fix-final.md**](variable-product-fix-final.md) - Variable product handling fixes
- [**variant-product-edit-fix.md**](variant-product-edit-fix.md) - Product variant editing improvements

### **Checkout & Orders**
- [**checkout-variant-changes.md**](checkout-variant-changes.md) - Checkout process modifications for variants
- [**checkout-wilaya-hiding.md**](checkout-wilaya-hiding.md) - Wilaya field management in checkout

## 🖼️ Media & Assets

### **Image Management**
- [**IMAGE_UPLOAD_SYSTEM.md**](IMAGE_UPLOAD_SYSTEM.md) - Image upload and processing system
- [**UNIFIED_IMAGE_STORAGE.md**](UNIFIED_IMAGE_STORAGE.md) - Unified image storage solution
- [**image-upload-production-fix.md**](image-upload-production-fix.md) - 🚨 **CRITICAL**: Production image upload fix for standalone builds
- [**image-error-fixes.md**](image-error-fixes.md) - Common image-related error fixes

## 🎨 UI & Styling

### **Authentication Pages**
- [**auth-pages-color-fix.md**](auth-pages-color-fix.md) - Authentication page styling fixes

## 🔧 System Maintenance

### **Email System**
- [**EMAIL_LOG_CLEANUP.md**](EMAIL_LOG_CLEANUP.md) - Email log cleanup and maintenance procedures

## 📖 How to Use This Guide

1. **Start Here**: Review this index to understand available documentation
2. **Critical Path**: Always check security and environment setup docs first
3. **Feature Work**: Reference relevant feature docs before making changes
4. **Troubleshooting**: Check diagnosis docs for known issues and solutions

## 📝 Documentation Standards

- **File Naming**: Use kebab-case with descriptive names
- **Headers**: Use clear hierarchy (##, ###)
- **Code Blocks**: Always include language tags
- **Cross-References**: Link related documentation
- **Update Index**: Add new docs to this index

## 🚀 Quick Start for New Developers

1. Read [**ENVIRONMENT_SETUP.md**](ENVIRONMENT_SETUP.md)
2. Review [**authentication-system.md**](authentication-system.md)
3. Check [**JWT_SECURITY_SETUP.md**](JWT_SECURITY_SETUP.md)
4. For payment work: [**thawani-integration-final.md**](thawani-integration-final.md)
5. For production issues: [**production-server-diagnosis.md**](production-server-diagnosis.md)

---

**Last Updated**: 2025/01/28  
**Total Documents**: 20  
**Maintenance**: Update this index when adding new documentation

#!/usr/bin/env node

/**
 * Upload System Diagnostic Script
 * Tests image upload functionality and identifies issues
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 Upload System Diagnostic');
console.log('==========================');

// Check Node.js version
console.log('\n📦 Node.js Version:');
try {
  const nodeVersion = execSync('node --version', { encoding: 'utf8' }).trim();
  console.log(`✅ ${nodeVersion}`);
} catch (error) {
  console.log('❌ Could not determine Node.js version');
}

// Check if <PERSON> is installed
console.log('\n🖼️  Sharp Installation:');
try {
  const sharp = require('sharp');
  console.log(`✅ Sharp ${sharp.versions.sharp} is installed`);

  // Test Sharp functionality
  sharp().toBuffer().then(() => {
    console.log('✅ <PERSON> is working correctly');
  }).catch(err => {
    console.log(`❌ Sharp test failed: ${err.message}`);
  });
} catch (error) {
  console.log('❌ Sharp is not installed or not working');
  console.log('   Run: npm install sharp');
}

// Check upload directories
console.log('\n📁 Upload Directories:');
const uploadDirs = [
  'public/uploads',
  'public/uploads/products',
  'public/uploads/categories',
  'public/uploads/branding'
];

uploadDirs.forEach(dir => {
  try {
    if (fs.existsSync(dir)) {
      const stats = fs.statSync(dir);
      console.log(`✅ ${dir} exists (${stats.mode.toString(8)})`);

      // Check write permissions
      try {
        fs.accessSync(dir, fs.constants.W_OK);
        console.log(`   ✅ Write permissions OK`);
      } catch {
        console.log(`   ❌ No write permissions`);
      }
    } else {
      console.log(`❌ ${dir} does not exist`);
    }
  } catch (error) {
    console.log(`❌ Error checking ${dir}: ${error.message}`);
  }
});

// Check file counts
console.log('\n📊 Upload Statistics:');
uploadDirs.forEach(dir => {
  try {
    if (fs.existsSync(dir)) {
      const files = fs.readdirSync(dir);
      const imageFiles = files.filter(file =>
        /\.(jpg|jpeg|png|webp|gif)$/i.test(file)
      );
      console.log(`📁 ${dir}: ${imageFiles.length} images`);
    }
  } catch (error) {
    console.log(`❌ Error reading ${dir}: ${error.message}`);
  }
});

// Check package.json dependencies
console.log('\n📋 Dependencies Check:');
try {
  const packageJson = require('../package.json');
  const deps = packageJson.dependencies || {};

  const requiredDeps = ['sharp', 'form-data'];
  requiredDeps.forEach(dep => {
    if (deps[dep]) {
      console.log(`✅ ${dep}: ${deps[dep]}`);
    } else {
      console.log(`❌ ${dep}: NOT FOUND`);
    }
  });
} catch (error) {
  console.log(`❌ Could not read package.json: ${error.message}`);
}

// Check environment variables
console.log('\n🔧 Environment Check:');
const envVars = ['NODE_ENV', 'VERCEL_URL', 'VERCEL_ENV'];
envVars.forEach(env => {
  const value = process.env[env];
  if (value) {
    console.log(`✅ ${env}: ${value}`);
  } else {
    console.log(`⚠️  ${env}: Not set`);
  }
});

// Test file system operations
console.log('\n💾 File System Test:');
try {
  const testDir = 'public/uploads/test';
  const testFile = path.join(testDir, 'test.txt');

  // Create test directory
  fs.mkdirSync(testDir, { recursive: true });
  console.log('✅ Can create directories');

  // Write test file
  fs.writeFileSync(testFile, 'test');
  console.log('✅ Can write files');

  // Read test file
  const content = fs.readFileSync(testFile, 'utf8');
  console.log(`✅ Can read files (${content})`);

  // Delete test file
  fs.unlinkSync(testFile);
  console.log('✅ Can delete files');

  // Delete test directory
  fs.rmdirSync(testDir);
  console.log('✅ Can delete directories');

} catch (error) {
  console.log(`❌ File system test failed: ${error.message}`);
}

// Recommendations
console.log('\n💡 Recommendations:');
console.log('==================');

console.log('\n1. If Sharp is missing:');
console.log('   npm install sharp');

console.log('\n2. If permissions are wrong:');
console.log('   chmod -R 755 public/uploads');
console.log('   chown -R www-data:www-data public/uploads  # or your web server user');

console.log('\n3. If on Vercel/Netlify:');
console.log('   - Ensure uploads directory is writable');
console.log('   - Check serverless function limits');
console.log('   - Verify Sharp is in production dependencies');

console.log('\n4. Test upload with a small image first');

console.log('\n5. Check browser console for detailed error messages');

console.log('\n✅ Diagnostic completed!');

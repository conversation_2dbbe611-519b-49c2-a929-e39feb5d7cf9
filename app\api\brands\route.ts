import { NextRequest, NextResponse } from 'next/server'
import prisma from '@/lib/prisma'
import { requireAdmin } from '@/lib/auth'

// GET /api/brands - Fetch all brands
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const search = searchParams.get('search')
    const showInMenu = searchParams.get('showInMenu')
    const showInHomepage = searchParams.get('showInHomepage')

    const where: any = {}

    if (status) {
      where.status = status
    }

    if (search) {
      where.OR = [
        { nameAr: { contains: search } },
        { nameEn: { contains: search } },
        { slug: { contains: search } }
      ]
    }

    if (showInMenu !== null) {
      where.showInMenu = showInMenu === 'true'
    }

    if (showInHomepage !== null) {
      where.showInHomepage = showInHomepage === 'true'
    }

    const brands = await prisma.brand.findMany({
      where,
      orderBy: [
        { order: 'asc' },
        { nameEn: 'asc' }
      ],
      include: {
        _count: {
          select: { product: true }
        }
      }
    })

    const response = NextResponse.json(brands)

    // AGGRESSIVE cache prevention with timestamp
    response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0')
    response.headers.set('Pragma', 'no-cache')
    response.headers.set('Expires', 'Thu, 01 Jan 1970 00:00:00 GMT')
    response.headers.set('Last-Modified', new Date().toUTCString())
    response.headers.set('X-Data-Timestamp', Date.now().toString())
    response.headers.set('X-Cache-Prevention', 'true')

    return response
  } catch (error) {
    console.error('Error fetching brands:', error)
    return NextResponse.json({ error: 'Failed to fetch brands' }, { status: 500 })
  }
}

// POST /api/brands - Create a new brand
export async function POST(request: NextRequest) {
  try {
    // Require admin authentication
    const authResult = await requireAdmin(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Not authenticated' ? 401 : 403 }
      )
    }

    const data = await request.json()

    // Check if slug already exists
    if (data.slug) {
      const existingBrand = await prisma.brand.findUnique({
        where: { slug: data.slug }
      })

      if (existingBrand) {
        return NextResponse.json({ error: 'Brand with this slug already exists' }, { status: 400 })
      }
    }

    const brand = await prisma.brand.create({
      data: {

        nameAr: data.nameAr,
        nameEn: data.nameEn,
        slug: data.slug,
        description: data.description || null,
        image: data.image || null,
        order: data.order || 1,
        status: data.status || 'active',
        showInMenu: data.showInMenu !== undefined ? data.showInMenu : true,
        showInHomepage: data.showInHomepage !== undefined ? data.showInHomepage : true,
        metaTitle: data.metaTitle || null,
        metaDescription: data.metaDescription || null

      },
      include: {
        _count: {
          select: { product: true }
        }
      }
    })

    return NextResponse.json(brand)
  } catch (error) {
    console.error('Error creating brand:', error)
    return NextResponse.json({ error: 'Failed to create brand' }, { status: 500 })
  }
}
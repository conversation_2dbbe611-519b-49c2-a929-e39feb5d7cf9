import { NextRequest, NextResponse } from 'next/server'
import prisma from '@/lib/prisma'
import { requireAdmin } from '@/lib/auth'
import { revalidatePath } from 'next/cache'

// GET all categories
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const status = searchParams.get('status')
    const search = searchParams.get('search')
    const showInMenu = searchParams.get('showInMenu')
    const showInHomepage = searchParams.get('showInHomepage')

    const where: any = {}

    if (status && status !== 'all') {
      where.status = status
    } else if (!status) {
      // Default to active if no status is specified and we're filtering by showInMenu or showInHomepage
      if (showInMenu === 'true' || showInHomepage === 'true') {
        where.status = 'active'
      }
    }

    if (search) {
      where.OR = [
        { nameAr: { contains: search } },
        { nameEn: { contains: search } },
        { slug: { contains: search } }
      ]
    }

    // Add filters for showInMenu and showInHomepage
    if (showInMenu === 'true') {
      where.showInMenu = true
    }
    if (showInHomepage === 'true') {
      where.showInHomepage = true
    }

    const categories = await prisma.category.findMany({
      where,
      orderBy: [
        { order: 'asc' },
        { createdAt: 'desc' }
      ],
      include: {
        _count: {
          select: { product: true }
        }
      }
    })

    const response = NextResponse.json({ categories })

    // AGGRESSIVE cache prevention with timestamp
    response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0')
    response.headers.set('Pragma', 'no-cache')
    response.headers.set('Expires', 'Thu, 01 Jan 1970 00:00:00 GMT')
    response.headers.set('Last-Modified', new Date().toUTCString())
    response.headers.set('X-Data-Timestamp', Date.now().toString())
    response.headers.set('X-Cache-Prevention', 'true')

    return response
  } catch (error) {
    console.error('Error fetching categories:', error)
    return NextResponse.json(
      { error: 'Failed to fetch categories' },
      { status: 500 }
    )
  }
}

// POST create new category
export async function POST(request: NextRequest) {
  try {
    // Require admin authentication
    const authResult = await requireAdmin(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Not authenticated' ? 401 : 403 }
      )
    }

    const body = await request.json()

    // Check if slug already exists
    const existingCategory = await prisma.category.findUnique({
      where: { slug: body.slug }
    })

    if (existingCategory) {
      return NextResponse.json(
        { error: 'Category with this slug already exists' },
        { status: 400 }
      )
    }

    const category = await prisma.category.create({
      data: {

        nameAr: body.nameAr,
        nameEn: body.nameEn,
        slug: body.slug,
        description: body.description,
        image: body.image,
        order: body.order || 1,
        status: body.status || 'active',
        showInMenu: body.showInMenu !== undefined ? body.showInMenu : true,
        showInHomepage: body.showInHomepage !== undefined ? body.showInHomepage : true,
        metaTitle: body.metaTitle,
        metaDescription: body.metaDescription

      }
    })

    // Revalidate pages that display categories to clear cache
    revalidatePath('/')  // Home page
    revalidatePath('/products')  // Products page

    return NextResponse.json(category, { status: 201 })
  } catch (error) {
    console.error('Error creating category:', error)
    return NextResponse.json(
      { error: 'Failed to create category' },
      { status: 500 }
    )
  }
}
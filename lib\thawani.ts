/**
 * Thawani Payment Gateway Service
 * Based on WooCommerce implementation pattern
 */

// API URLs following WooCommerce pattern
const THAWANI_TEST_URL = 'https://uatcheckout.thawani.om/api/v1/checkout/session'
const THAWANI_PROD_URL = 'https://checkout.thawani.om/api/v1/checkout/session'
const THAWANI_TEST_CHECKOUT = 'https://uatcheckout.thawani.om/pay'
const THAWANI_PROD_CHECKOUT = 'https://checkout.thawani.om/pay'

export interface ThawaniSettings {
  thawaniEnabled?: boolean
  thawaniTestMode?: boolean
  thawaniPublishableKey?: string
  thawaniSecretKey?: string
  thawaniTestPublishableKey?: string
  thawaniTestSecretKey?: string
}

export interface ThawaniProduct {
  name: string
  unit_amount: number // Amount in baisa (1 OMR = 1000 baisa)
  quantity: number
}

export interface ThawaniMetadata {
  customer_name: string
  customer_email: string
  customer_phone: string
  order_id: string
  order_number?: string
  merchant_name?: string
  merchant_logo?: string
  [key: string]: any
}

export interface ThawaniSessionRequest {
  client_reference_id: string
  products: ThawaniProduct[]
  metadata: ThawaniMetadata
  success_url: string
  cancel_url: string
  return_url?: string
  callback_url?: string
}

export interface ThawaniSessionResponse {
  success: boolean
  code?: number
  description?: string
  data?: {
    session_id: string
    client_reference_id?: string
    customer_email?: string
    created?: string
    status?: string
    amount_total?: number
    currency?: string
  }
}

/**
 * Get the appropriate API URL based on test mode
 */
function getApiUrl(isTestMode: boolean): string {
  return isTestMode ? THAWANI_TEST_URL : THAWANI_PROD_URL
}

/**
 * Get the appropriate checkout URL based on test mode
 */
function getCheckoutUrl(isTestMode: boolean): string {
  return isTestMode ? THAWANI_TEST_CHECKOUT : THAWANI_PROD_CHECKOUT
}

/**
 * Get the appropriate API key based on test mode
 */
function getApiKey(settings: ThawaniSettings): string {
  if (settings.thawaniTestMode) {
    return settings.thawaniTestSecretKey || ''
  }
  return settings.thawaniSecretKey || ''
}

/**
 * Get the appropriate publishable key based on test mode
 */
function getPublishableKey(settings: ThawaniSettings): string {
  if (settings.thawaniTestMode) {
    return settings.thawaniTestPublishableKey || ''
  }
  return settings.thawaniPublishableKey || ''
}

/**
 * Create a checkout session with Thawani
 * Following the exact pattern from WooCommerce implementation
 */
export async function createCheckoutSession(
  orderId: string,
  orderNumber: string,
  userId: string,
  total: number, // Total in OMR
  customerName: string,
  customerEmail: string,
  customerPhone: string,
  settings: ThawaniSettings,
  baseUrl: string,
  storeInfo?: { storeName?: string; storeLogo?: string | null }
): Promise<{ success: boolean; sessionId?: string; redirectUrl?: string; error?: string }> {

  const isTestMode = settings.thawaniTestMode || false
  const apiUrl = getApiUrl(isTestMode)
  const secretKey = getApiKey(settings)
  const publishableKey = getPublishableKey(settings)

  if (!secretKey || !publishableKey) {
    return {
      success: false,
      error: 'Thawani API keys not configured'
    }
  }

  // Convert total to baisa (multiply by 1000)
  const amountInBaisa = Math.round(total * 1000)

  // Get store name for product description
  const _storeName = storeInfo?.storeName || 'Shop'

  // Prepare payload following WooCommerce pattern
  // Keep metadata simple to avoid validation issues
  const metadata: ThawaniMetadata = {
    customer_name: customerName,
    customer_email: customerEmail,
    customer_phone: customerPhone.replace(/\+/g, ''), // Remove + from phone number
    order_id: orderId,  // Use database ID for security (UUID instead of sequential number)
    order_number: orderNumber  // Keep order number for display purposes
  }

  const payload: ThawaniSessionRequest = {
    client_reference_id: orderNumber, // Use order number as reference
    products: [
      {
        name: `Order #${orderNumber}`,  // Simplified product name
        unit_amount: amountInBaisa,
        quantity: 1
      }
    ],
    metadata,
    success_url: `${baseUrl}/order/${orderId}`,
    cancel_url: `${baseUrl}/order/${orderId}`,
    return_url: `${baseUrl}/order/${orderId}`,
    callback_url: `${baseUrl}/api/payment/thawani/callback`
  }

  // Log payload for debugging
  console.log('Thawani API Request:', {
    url: apiUrl,
    testMode: isTestMode,
    payload: JSON.stringify(payload, null, 2)
  })

  try {
    // Make API request following WooCommerce pattern
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Thawani-Api-Key': secretKey,
        'Content-Type': 'application/json; charset=utf-8'
      },
      body: JSON.stringify(payload)
    })

    const result: ThawaniSessionResponse = await response.json()

    // Log the response for debugging
    console.log('Thawani API Response:', {
      status: response.status,
      success: result.success,
      description: result.description,
      code: result.code,
      data: result.data
    })

    // Log detailed error if present
    if ((result.data as any)?.error) {
      console.log('Thawani Validation Errors:', JSON.stringify((result.data as any).error, null, 2))
    }

    if (response.status === 200 && result.success && result.data?.session_id) {
      const sessionId = result.data.session_id
      const checkoutUrl = getCheckoutUrl(isTestMode)
      const redirectUrl = `${checkoutUrl}/${sessionId}?key=${publishableKey}`

      return {
        success: true,
        sessionId,
        redirectUrl
      }
    } else {
      // More detailed error message
      const errorMsg = result.description || result.code
        ? `${result.description || 'Failed to create session'} (Code: ${result.code || 'unknown'})`
        : 'Failed to create Thawani session'

      console.error('Thawani session creation failed:', errorMsg)

      return {
        success: false,
        error: errorMsg
      }
    }
  } catch (error) {
    console.error('Thawani API error:', error)
    return {
      success: false,
      error: 'Could not send payment request to Thawani'
    }
  }
}

/**
 * Get session details from Thawani by session ID
 */
export async function getSessionDetails(
  sessionId: string,
  settings: ThawaniSettings
): Promise<{ success: boolean; data?: any; error?: string }> {

  const isTestMode = settings.thawaniTestMode || false
  const apiUrl = getApiUrl(isTestMode).replace('/session', `/session/${sessionId}`)
  const secretKey = getApiKey(settings)

  if (!secretKey) {
    return {
      success: false,
      error: 'Thawani API key not configured'
    }
  }

  try {
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Thawani-Api-Key': secretKey,
        'Content-Type': 'application/json'
      }
    })

    const result = await response.json()

    if (response.status === 200 && result.success) {
      return {
        success: true,
        data: result.data
      }
    } else {
      return {
        success: false,
        error: result.description || 'Failed to get session details'
      }
    }
  } catch (error) {
    console.error('Thawani API error:', error)
    return {
      success: false,
      error: 'Could not retrieve session from Thawani'
    }
  }
}

/**
 * Get session details from Thawani by invoice ID (for webhooks)
 */
export async function getSessionByInvoice(
  invoiceId: string,
  settings: ThawaniSettings
): Promise<{ success: boolean; data?: any; error?: string }> {

  const isTestMode = settings.thawaniTestMode || false
  // Use the invoice endpoint pattern (following PHP get_session_by_invoice pattern)
  const baseUrl = isTestMode ? 'https://uatcheckout.thawani.om/api/v1' : 'https://checkout.thawani.om/api/v1'
  const apiUrl = `${baseUrl}/checkout/invoice/${invoiceId}`
  const secretKey = getApiKey(settings)

  if (!secretKey) {
    return {
      success: false,
      error: 'Thawani API key not configured'
    }
  }

  try {
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Thawani-Api-Key': secretKey,
        'Content-Type': 'application/json'
      }
    })

    const result = await response.json()

    if (response.status === 200 && result.success) {
      return {
        success: true,
        data: result.data
      }
    } else {
      return {
        success: false,
        error: result.description || 'Failed to get session by invoice'
      }
    }
  } catch (error) {
    console.error('Thawani API error:', error)
    return {
      success: false,
      error: 'Could not retrieve session by invoice from Thawani'
    }
  }
}

/**
 * Validate callback parameters
 */
export function validateCallback(params: URLSearchParams): {
  isValid: boolean
  orderId?: string
  status?: 'success' | 'fail' | 'webhook'
  error?: string
} {
  const orderId = params.get('order')
  const status = params.get('status')

  if (!orderId) {
    return {
      isValid: false,
      error: 'Order ID missing from callback'
    }
  }

  // If no status parameter, this is a webhook callback
  if (!status) {
    return {
      isValid: true,
      orderId,
      status: 'webhook'
    }
  }

  // If status parameter exists, validate it
  if (status !== 'success' && status !== 'fail') {
    return {
      isValid: false,
      error: 'Invalid payment status'
    }
  }

  return {
    isValid: true,
    orderId,
    status: status as 'success' | 'fail'
  }
}

/**
 * Check if Thawani is properly configured and enabled
 */
export function isThawaniConfigured(settings: ThawaniSettings): boolean {
  if (!settings.thawaniEnabled) {
    return false
  }

  if (settings.thawaniTestMode) {
    return !!(settings.thawaniTestPublishableKey && settings.thawaniTestSecretKey)
  }

  return !!(settings.thawaniPublishableKey && settings.thawaniSecretKey)
}
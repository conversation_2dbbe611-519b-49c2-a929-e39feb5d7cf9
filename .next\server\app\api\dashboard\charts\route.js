/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/dashboard/charts/route";
exports.ids = ["app/api/dashboard/charts/route"];
exports.modules = {

/***/ "(rsc)/./app/api/dashboard/charts/route.ts":
/*!*******************************************!*\
  !*** ./app/api/dashboard/charts/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _barrel_optimize_names_eachDayOfInterval_endOfDay_format_startOfDay_subDays_date_fns__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=eachDayOfInterval,endOfDay,format,startOfDay,subDays!=!date-fns */ \"(rsc)/./node_modules/date-fns/endOfDay.js\");\n/* harmony import */ var _barrel_optimize_names_eachDayOfInterval_endOfDay_format_startOfDay_subDays_date_fns__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=eachDayOfInterval,endOfDay,format,startOfDay,subDays!=!date-fns */ \"(rsc)/./node_modules/date-fns/startOfDay.js\");\n/* harmony import */ var _barrel_optimize_names_eachDayOfInterval_endOfDay_format_startOfDay_subDays_date_fns__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=eachDayOfInterval,endOfDay,format,startOfDay,subDays!=!date-fns */ \"(rsc)/./node_modules/date-fns/subDays.js\");\n/* harmony import */ var _barrel_optimize_names_eachDayOfInterval_endOfDay_format_startOfDay_subDays_date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=eachDayOfInterval,endOfDay,format,startOfDay,subDays!=!date-fns */ \"(rsc)/./node_modules/date-fns/eachDayOfInterval.js\");\n/* harmony import */ var _barrel_optimize_names_eachDayOfInterval_endOfDay_format_startOfDay_subDays_date_fns__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=eachDayOfInterval,endOfDay,format,startOfDay,subDays!=!date-fns */ \"(rsc)/./node_modules/date-fns/format.js\");\n\n\n\n\nasync function GET(request) {\n    try {\n        // Require admin authentication\n        const authResult = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.requireAdmin)(request);\n        if (!authResult.success) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: authResult.error\n            }, {\n                status: authResult.error === 'Not authenticated' ? 401 : 403\n            });\n        }\n        const searchParams = request.nextUrl.searchParams;\n        const days = parseInt(searchParams.get('days') || '7');\n        const endDate = (0,_barrel_optimize_names_eachDayOfInterval_endOfDay_format_startOfDay_subDays_date_fns__WEBPACK_IMPORTED_MODULE_3__.endOfDay)(new Date());\n        const startDate = (0,_barrel_optimize_names_eachDayOfInterval_endOfDay_format_startOfDay_subDays_date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfDay)((0,_barrel_optimize_names_eachDayOfInterval_endOfDay_format_startOfDay_subDays_date_fns__WEBPACK_IMPORTED_MODULE_5__.subDays)(endDate, days - 1));\n        // Get all orders in the date range\n        const orders = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__[\"default\"].order.findMany({\n            where: {\n                createdAt: {\n                    gte: startDate,\n                    lte: endDate\n                },\n                status: {\n                    not: 'cancelled'\n                }\n            },\n            include: {\n                orderitem: {\n                    include: {\n                        product: {\n                            include: {\n                                category: true\n                            }\n                        }\n                    }\n                }\n            }\n        });\n        // Get customer registrations in the date range\n        const customers = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__[\"default\"].user.findMany({\n            where: {\n                role: 'customer',\n                createdAt: {\n                    gte: startDate,\n                    lte: endDate\n                }\n            },\n            select: {\n                createdAt: true\n            }\n        });\n        // Generate daily revenue data\n        const dateRange = (0,_barrel_optimize_names_eachDayOfInterval_endOfDay_format_startOfDay_subDays_date_fns__WEBPACK_IMPORTED_MODULE_6__.eachDayOfInterval)({\n            start: startDate,\n            end: endDate\n        });\n        const dailyRevenue = dateRange.map((date)=>{\n            const dayStart = (0,_barrel_optimize_names_eachDayOfInterval_endOfDay_format_startOfDay_subDays_date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfDay)(date);\n            const dayEnd = (0,_barrel_optimize_names_eachDayOfInterval_endOfDay_format_startOfDay_subDays_date_fns__WEBPACK_IMPORTED_MODULE_3__.endOfDay)(date);\n            const dayOrders = orders.filter((order)=>{\n                const orderDate = new Date(order.createdAt);\n                return orderDate >= dayStart && orderDate <= dayEnd;\n            });\n            const revenue = dayOrders.reduce((sum, order)=>{\n                return sum + parseFloat(order.total?.toString() || '0');\n            }, 0);\n            const orderCount = dayOrders.length;\n            return {\n                date: (0,_barrel_optimize_names_eachDayOfInterval_endOfDay_format_startOfDay_subDays_date_fns__WEBPACK_IMPORTED_MODULE_7__.format)(date, 'MMM dd'),\n                revenue,\n                orders: orderCount\n            };\n        });\n        // Generate customer growth data\n        const dailyCustomers = dateRange.map((date)=>{\n            const dayStart = (0,_barrel_optimize_names_eachDayOfInterval_endOfDay_format_startOfDay_subDays_date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfDay)(date);\n            const dayEnd = (0,_barrel_optimize_names_eachDayOfInterval_endOfDay_format_startOfDay_subDays_date_fns__WEBPACK_IMPORTED_MODULE_3__.endOfDay)(date);\n            const dayCustomers = customers.filter((customer)=>{\n                const customerDate = new Date(customer.createdAt);\n                return customerDate >= dayStart && customerDate <= dayEnd;\n            });\n            return {\n                date: (0,_barrel_optimize_names_eachDayOfInterval_endOfDay_format_startOfDay_subDays_date_fns__WEBPACK_IMPORTED_MODULE_7__.format)(date, 'MMM dd'),\n                count: dayCustomers.length\n            };\n        });\n        // Calculate revenue by category\n        const categoryRevenue = {};\n        orders.forEach((order)=>{\n            order.orderitem.forEach((item)=>{\n                if (item.product?.category) {\n                    const categoryId = item.product.category.id;\n                    if (!categoryRevenue[categoryId]) {\n                        categoryRevenue[categoryId] = {\n                            nameAr: item.product.category.nameAr,\n                            nameEn: item.product.category.nameEn || '',\n                            revenue: 0\n                        };\n                    }\n                    categoryRevenue[categoryId].revenue += parseFloat(item.price?.toString() || '0') * item.quantity;\n                }\n            });\n        });\n        const categoryRevenueArray = Object.values(categoryRevenue).sort((a, b)=>b.revenue - a.revenue).slice(0, 5);\n        // Get top products by quantity sold\n        const productSales = {};\n        orders.forEach((order)=>{\n            order.orderitem.forEach((item)=>{\n                if (item.product) {\n                    const productId = item.product.id;\n                    if (!productSales[productId]) {\n                        productSales[productId] = {\n                            product: item.product,\n                            quantity: 0,\n                            revenue: 0\n                        };\n                    }\n                    productSales[productId].quantity += item.quantity;\n                    productSales[productId].revenue += parseFloat(item.price?.toString() || '0') * item.quantity;\n                }\n            });\n        });\n        const topProducts = Object.values(productSales).sort((a, b)=>b.quantity - a.quantity).slice(0, 10).map((item)=>({\n                nameAr: item.product.nameAr,\n                nameEn: item.product.nameEn,\n                quantity: item.quantity,\n                revenue: item.revenue\n            }));\n        // Order status distribution (all time)\n        const allOrderStatuses = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__[\"default\"].order.groupBy({\n            by: [\n                'status'\n            ],\n            _count: {\n                status: true\n            }\n        });\n        const orderStatusData = allOrderStatuses.map((item)=>({\n                status: item.status,\n                count: item._count.status\n            }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            dailyRevenue,\n            dailyCustomers,\n            categoryRevenue: categoryRevenueArray,\n            topProducts,\n            orderStatusDistribution: orderStatusData,\n            period: {\n                days,\n                startDate,\n                endDate\n            }\n        });\n    } catch (error) {\n        console.error('Dashboard charts error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch chart data'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/dashboard/charts/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AUTH_STORAGE_KEY: () => (/* binding */ AUTH_STORAGE_KEY),\n/* harmony export */   cleanupExpiredSessions: () => (/* binding */ cleanupExpiredSessions),\n/* harmony export */   clearClientAuthState: () => (/* binding */ clearClientAuthState),\n/* harmony export */   createSession: () => (/* binding */ createSession),\n/* harmony export */   generateSessionToken: () => (/* binding */ generateSessionToken),\n/* harmony export */   getClientAuthState: () => (/* binding */ getClientAuthState),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getUserSessions: () => (/* binding */ getUserSessions),\n/* harmony export */   invalidateSession: () => (/* binding */ invalidateSession),\n/* harmony export */   requireAdmin: () => (/* binding */ requireAdmin),\n/* harmony export */   requireUserOrAdmin: () => (/* binding */ requireUserOrAdmin),\n/* harmony export */   setClientAuthState: () => (/* binding */ setClientAuthState)\n/* harmony export */ });\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n// Secure authentication utilities\n\n\n/**\r\n * Generate a cryptographically secure session token\r\n */ function generateSessionToken() {\n    return (0,crypto__WEBPACK_IMPORTED_MODULE_0__.randomBytes)(32).toString('hex');\n}\n/**\r\n * Create a new session in the database\r\n */ async function createSession(userId, userAgent, ipAddress, rememberMe = false) {\n    const token = generateSessionToken();\n    const expiresAt = new Date();\n    // Set expiration based on remember me preference\n    if (rememberMe) {\n        expiresAt.setDate(expiresAt.getDate() + 30); // 30 days\n    } else {\n        expiresAt.setDate(expiresAt.getDate() + 1); // 1 day\n    }\n    await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__[\"default\"].session.create({\n        data: {\n            token,\n            userId,\n            userAgent,\n            ipAddress,\n            expiresAt\n        }\n    });\n    return {\n        token,\n        expiresAt\n    };\n}\n/**\r\n * Server-side function to get current user from session token\r\n * Use this in API routes for authentication\r\n */ async function getCurrentUser(request) {\n    try {\n        // Get session cookie (port-specific for development)\n        const port = request.headers.get('host')?.split(':')[1] || '3000';\n        const sessionCookieName = `session-${port}`;\n        const sessionCookie = request.cookies.get(sessionCookieName);\n        if (!sessionCookie) {\n            return {\n                success: false,\n                error: 'Not authenticated'\n            };\n        }\n        // Get session from database using token\n        const session = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__[\"default\"].session.findUnique({\n            where: {\n                token: sessionCookie.value\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        nameAr: true,\n                        nameEn: true,\n                        phone: true,\n                        role: true,\n                        isActive: true,\n                        emailVerified: true\n                    }\n                }\n            }\n        });\n        if (!session) {\n            return {\n                success: false,\n                error: 'Invalid session'\n            };\n        }\n        // Check if session has expired\n        if (session.expiresAt < new Date()) {\n            // Clean up expired session\n            await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__[\"default\"].session.delete({\n                where: {\n                    id: session.id\n                }\n            });\n            return {\n                success: false,\n                error: 'Session expired'\n            };\n        }\n        if (!session.user) {\n            return {\n                success: false,\n                error: 'User not found'\n            };\n        }\n        if (!session.user.isActive) {\n            return {\n                success: false,\n                error: 'Account is not active'\n            };\n        }\n        // Update session last accessed time\n        await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__[\"default\"].session.update({\n            where: {\n                id: session.id\n            },\n            data: {\n                updatedAt: new Date()\n            }\n        });\n        return {\n            success: true,\n            user: session.user\n        };\n    } catch (error) {\n        console.error('Error getting current user:', error);\n        return {\n            success: false,\n            error: 'Authentication error'\n        };\n    }\n}\n/**\r\n * Check if user has admin role\r\n * Always validates against database, never trusts client data\r\n */ async function requireAdmin(request) {\n    const authResult = await getCurrentUser(request);\n    if (!authResult.success || !authResult.user) {\n        return authResult;\n    }\n    if (authResult.user.role !== 'admin') {\n        return {\n            success: false,\n            error: 'Admin access required'\n        };\n    }\n    return authResult;\n}\n/**\r\n * Check if user has access to resource (either admin or owns the resource)\r\n */ async function requireUserOrAdmin(request, resourceUserId) {\n    const authResult = await getCurrentUser(request);\n    if (!authResult.success || !authResult.user) {\n        return authResult;\n    }\n    const user = authResult.user;\n    // Admin can access anything\n    if (user.role === 'admin') {\n        return authResult;\n    }\n    // User can only access their own resources\n    if (user.id === resourceUserId) {\n        return authResult;\n    }\n    return {\n        success: false,\n        error: 'Access denied'\n    };\n}\n/**\r\n * Invalidate a session (logout)\r\n */ async function invalidateSession(token) {\n    try {\n        await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__[\"default\"].session.delete({\n            where: {\n                token\n            }\n        });\n    } catch (error) {\n        // Session might already be deleted, which is fine\n        console.error('Error invalidating session:', error);\n    }\n}\n/**\r\n * Clean up expired sessions (should be run periodically)\r\n */ async function cleanupExpiredSessions() {\n    try {\n        const result = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__[\"default\"].session.deleteMany({\n            where: {\n                expiresAt: {\n                    lt: new Date()\n                }\n            }\n        });\n        return result.count;\n    } catch (error) {\n        console.error('Error cleaning up expired sessions:', error);\n        return 0;\n    }\n}\n/**\r\n * Get all active sessions for a user\r\n */ async function getUserSessions(userId) {\n    try {\n        return await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__[\"default\"].session.findMany({\n            where: {\n                userId,\n                expiresAt: {\n                    gt: new Date()\n                }\n            },\n            select: {\n                id: true,\n                userAgent: true,\n                ipAddress: true,\n                createdAt: true,\n                updatedAt: true,\n                expiresAt: true\n            },\n            orderBy: {\n                updatedAt: 'desc'\n            }\n        });\n    } catch (error) {\n        console.error('Error getting user sessions:', error);\n        return [];\n    }\n}\n// This will be used by a React hook to replace localStorage user data\nconst AUTH_STORAGE_KEY = 'auth_state';\nfunction getClientAuthState() {\n    if (true) {\n        return {\n            isAuthenticated: false,\n            isAdmin: false,\n            userId: null,\n            loading: true\n        };\n    }\n    try {\n        const stored = localStorage.getItem(AUTH_STORAGE_KEY);\n        if (stored) {\n            return JSON.parse(stored);\n        }\n    } catch (error) {\n        console.error('Error reading auth state:', error);\n        localStorage.removeItem(AUTH_STORAGE_KEY);\n    }\n    return {\n        isAuthenticated: false,\n        isAdmin: false,\n        userId: null,\n        loading: false\n    };\n}\nfunction setClientAuthState(state) {\n    if (true) return;\n    try {\n        localStorage.setItem(AUTH_STORAGE_KEY, JSON.stringify(state));\n    } catch (error) {\n        console.error('Error storing auth state:', error);\n    }\n}\nfunction clearClientAuthState() {\n    if (true) return;\n    localStorage.removeItem(AUTH_STORAGE_KEY);\n    localStorage.removeItem('user'); // Remove old insecure storage\n    localStorage.removeItem('rememberedEmail'); // Remove old insecure remember me\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst prismaClient = global.__prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) {\n    global.__prisma = prismaClient;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (prismaClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQU03QyxNQUFNQyxlQUFlQyxPQUFPQyxRQUFRLElBQUksSUFBSUgsd0RBQVlBO0FBRXhELElBQUlJLElBQXFDLEVBQUU7SUFDekNGLE9BQU9DLFFBQVEsR0FBR0Y7QUFDcEI7QUFFQSxpRUFBZUEsWUFBWUEsRUFBQSIsInNvdXJjZXMiOlsiRDpcXG5vZGVcXHdvbzJcXGxpYlxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50J1xuXG5kZWNsYXJlIGdsb2JhbCB7XG4gIHZhciBfX3ByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmNvbnN0IHByaXNtYUNsaWVudCA9IGdsb2JhbC5fX3ByaXNtYSB8fCBuZXcgUHJpc21hQ2xpZW50KClcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIHtcbiAgZ2xvYmFsLl9fcHJpc21hID0gcHJpc21hQ2xpZW50XG59XG5cbmV4cG9ydCBkZWZhdWx0IHByaXNtYUNsaWVudCJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJwcmlzbWFDbGllbnQiLCJnbG9iYWwiLCJfX3ByaXNtYSIsInByb2Nlc3MiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdashboard%2Fcharts%2Froute&page=%2Fapi%2Fdashboard%2Fcharts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fcharts%2Froute.ts&appDir=D%3A%5Cnode%5Cwoo2%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cnode%5Cwoo2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdashboard%2Fcharts%2Froute&page=%2Fapi%2Fdashboard%2Fcharts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fcharts%2Froute.ts&appDir=D%3A%5Cnode%5Cwoo2%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cnode%5Cwoo2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var D_node_woo2_app_api_dashboard_charts_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./app/api/dashboard/charts/route.ts */ \"(rsc)/./app/api/dashboard/charts/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/dashboard/charts/route\",\n        pathname: \"/api/dashboard/charts\",\n        filename: \"route\",\n        bundlePath: \"app/api/dashboard/charts/route\"\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    resolvedPagePath: \"D:\\\\node\\\\woo2\\\\app\\\\api\\\\dashboard\\\\charts\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_node_woo2_app_api_dashboard_charts_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/dashboard/charts/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdashboard%2Fcharts%2Froute&page=%2Fapi%2Fdashboard%2Fcharts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fcharts%2Froute.ts&appDir=D%3A%5Cnode%5Cwoo2%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cnode%5Cwoo2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/date-fns"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdashboard%2Fcharts%2Froute&page=%2Fapi%2Fdashboard%2Fcharts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fcharts%2Froute.ts&appDir=D%3A%5Cnode%5Cwoo2%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cnode%5Cwoo2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();
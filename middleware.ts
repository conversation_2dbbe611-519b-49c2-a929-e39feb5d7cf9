import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname

  // Skip cache prevention for static assets to avoid 502 errors
  if (pathname.startsWith('/_next/static') || 
      pathname.startsWith('/_next/image') || 
      pathname.includes('.') && 
      (pathname.endsWith('.js') || 
       pathname.endsWith('.css') || 
       pathname.endsWith('.ico') ||
       pathname.endsWith('.png') ||
       pathname.endsWith('.jpg') ||
       pathname.endsWith('.jpeg') ||
       pathname.endsWith('.webp'))) {
    return NextResponse.next()
  }

  // Apply cache prevention only to dynamic pages and API routes
  const response = NextResponse.next()

  // Add cache control headers for dynamic content only
  response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0')
  response.headers.set('Pragma', 'no-cache')
  response.headers.set('Expires', 'Thu, 01 Jan 1970 00:00:00 GMT')
  response.headers.set('Last-Modified', new Date().toUTCString())
  response.headers.set('X-Cache-Prevention', 'true')

  // Get port from the host header
  const port = request.headers.get('host')?.split(':')[1] || '3000'
  const sessionCookieName = `session-${port}`

  // Check if the request is for an admin route
  if (pathname.startsWith('/admin')) {
    // Get the session token cookie
    const sessionToken = request.cookies.get(sessionCookieName)

    // If no session token, redirect to login
    if (!sessionToken) {
      const loginUrl = new URL('/login', request.url)
      loginUrl.searchParams.set('redirect', pathname)
      return NextResponse.redirect(loginUrl)
    }

    // Note: We can't validate the session token in middleware since it requires database access
    // The actual role verification happens in the admin page component
    // If the session is invalid, the admin page will redirect automatically
  }

  // Check if user is already logged in and trying to access login page
  if (pathname === '/login') {
    const sessionToken = request.cookies.get(sessionCookieName)

    // If session exists, let the login page handle the redirect logic
    // based on the user's actual role (which requires database verification)
    if (sessionToken) {
      // The login page will check the session and redirect appropriately
      // We can't determine the role here without database access
    }
  }

  return NextResponse.next()
}

// Configure which routes the middleware should run on - exclude static assets
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - uploads (uploaded images)
     * - files with extensions (js, css, png, etc.)
     */
    '/((?!_next/static|_next/image|favicon.ico|uploads|.*\\.[^/]*$).*)',
    '/admin/:path*',
    '/login',
    '/api/(.*)'
  ]
}
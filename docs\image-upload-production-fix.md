# 🛠️ Image Upload Production Fix

**Issue**: Product image uploads work for old images but fail for new uploads in production  
**Root Cause**: Standalone Next.js build uses different directory structure  
**Status**: SOLUTION PROVIDED ✅

## 🎯 Problem Analysis

### **The Issue**
After deploying to production server using standalone Next.js build:
- ✅ Old images (uploaded in development) display correctly
- ❌ New image uploads fail or don't appear
- ❌ Uploaded images return 404 errors
- ❌ Admin panel shows "upload failed" errors

### **Root Cause**
In standalone Next.js builds, the application runs from `.next/standalone/` directory, but:
1. **Upload directory mismatch**: Code tries to save to `public/uploads/` 
2. **Missing directories**: `public/uploads/` doesn't exist in standalone build
3. **Nginx misconfiguration**: Server looks for uploads in wrong location
4. **Permission issues**: Upload directories lack write permissions

## 🔍 Diagnosis Commands

Run the diagnostic script to identify specific issues:

```bash
# Run diagnostic script
node scripts/diagnose-image-upload.js

# Check if standalone build
ls .next/standalone/

# Check current upload directory
ls -la public/uploads/
ls -la .next/standalone/public/uploads/

# Test nginx configuration
curl -I https://horseoud.com/uploads/products/test.jpg
```

## 🛠️ Complete Fix

### **Step 1: Run Diagnostic Script**

```bash
# Identify the specific issues
node scripts/diagnose-image-upload.js
```

### **Step 2: Apply Automated Fix**

```bash
# Run the fix script
node scripts/fix-image-upload-production.js

# Make scripts executable (if needed)
chmod +x scripts/*.js
```

### **Step 3: Update Nginx Configuration**

Update your nginx configuration to serve uploads from standalone directory:

```nginx
# Add to your server block
location /uploads/ {
    alias /www/wwwroot/node/ecommerc/.next/standalone/public/uploads/;
    expires 1d;
    add_header Cache-Control "public";
    try_files $uri =404;
}
```

### **Step 4: Restart Services**

```bash
# Restart Next.js application
pm2 restart horseoud

# Reload nginx
sudo nginx -t && sudo systemctl reload nginx
```

### **Step 5: Test Upload Functionality**

1. Go to admin panel: `https://horseoud.com/admin`
2. Try uploading a new product image
3. Verify image appears in product list
4. Check image is accessible: `https://horseoud.com/uploads/products/[filename]`

## 📋 Manual Fix (If Scripts Fail)

### **Create Upload Directories**

```bash
# Create directories in standalone build
mkdir -p .next/standalone/public/uploads/products
mkdir -p .next/standalone/public/uploads/categories
mkdir -p .next/standalone/public/uploads/branding

# Set proper permissions
chmod -R 755 .next/standalone/public/uploads/
chown -R www-data:www-data .next/standalone/public/uploads/
```

### **Copy Existing Uploads**

```bash
# Copy existing uploads to standalone directory
cp -r public/uploads/* .next/standalone/public/uploads/

# Verify files copied
ls -la .next/standalone/public/uploads/products/
```

### **Test Write Permissions**

```bash
# Test if uploads work
touch .next/standalone/public/uploads/.test-write
rm .next/standalone/public/uploads/.test-write
```

## 🔧 Nginx Configuration Update

### **Complete Server Block**

```nginx
server {
    listen 80;
    server_name horseoud.com www.horseoud.com;
    
    # Root points to standalone build
    root /www/wwwroot/node/ecommerc/.next/standalone;
    
    # Serve uploaded images directly
    location /uploads/ {
        alias /www/wwwroot/node/ecommerc/.next/standalone/public/uploads/;
        expires 1d;
        add_header Cache-Control "public";
        try_files $uri =404;
    }
    
    # Serve Next.js static assets
    location /_next/static/ {
        alias /www/wwwroot/node/ecommerc/.next/standalone/.next/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }
    
    # Proxy to Next.js app
    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## ✅ Verification Checklist

After applying the fix, verify:

- [ ] Upload directories exist in `.next/standalone/public/uploads/`
- [ ] Directories have proper permissions (755)
- [ ] Can create files in upload directories
- [ ] Nginx serves images from correct path
- [ ] New image uploads work in admin panel
- [ ] Uploaded images display on frontend
- [ ] Old images still work
- [ ] No 404 errors for image URLs

## 🚨 Common Issues & Solutions

### **Issue 1: Permission Denied**
```bash
# Fix: Set proper ownership and permissions
sudo chown -R www-data:www-data .next/standalone/public/uploads/
sudo chmod -R 755 .next/standalone/public/uploads/
```

### **Issue 2: Directory Not Found**
```bash
# Fix: Create missing directories
mkdir -p .next/standalone/public/uploads/{products,categories,branding}
```

### **Issue 3: Nginx 404 Errors**
```bash
# Fix: Check nginx configuration
sudo nginx -t
# Update alias path in nginx config
# Restart nginx: sudo systemctl reload nginx
```

### **Issue 4: Sharp Library Missing**
```bash
# Fix: Install Sharp in production
cd .next/standalone && npm install sharp
# Or rebuild application: npm run build
```

## 🔮 Prevention for Future Deployments

### **Build Process Update**

Add to your deployment script:

```bash
#!/bin/bash
# After build
npm run build

# Copy public assets to standalone
cp -r public/* .next/standalone/public/

# Set permissions
chmod -R 755 .next/standalone/public/uploads/

# Restart application
pm2 restart horseoud
```

### **Environment Variables**

Consider adding to `.env.production`:

```env
# Upload configuration
UPLOAD_PATH=/www/wwwroot/node/ecommerc/.next/standalone/public/uploads
STATIC_URL_PREFIX=/uploads
```

## 📊 Expected Results

**✅ After Fix:**
- New image uploads save to correct directory
- Images are immediately visible in admin and frontend
- All uploaded images return HTTP 200 status
- Old images continue to work
- Upload API responds without errors

**✅ Admin Panel:**
- Image upload progress shows 100% success
- Uploaded images appear in preview
- Product/category images save correctly
- No JavaScript console errors

**✅ Frontend:**
- Product images load without 404 errors
- Image optimization works correctly
- Page load speed remains optimal

## 🏃‍♂️ Quick Recovery Commands

```bash
# Emergency fix in 30 seconds
mkdir -p .next/standalone/public/uploads/{products,categories}
chmod -R 755 .next/standalone/public/uploads/
pm2 restart horseoud
sudo systemctl reload nginx

# Verify fix worked
curl -I https://horseoud.com/uploads/products/
```

---

**Priority**: HIGH  
**Estimated Fix Time**: 5-10 minutes  
**Business Impact**: Product management functionality restored

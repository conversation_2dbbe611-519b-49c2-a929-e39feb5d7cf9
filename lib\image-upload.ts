import sharp from 'sharp'
import path from 'path'
import fs from 'fs/promises'
import crypto from 'crypto'

// Image optimization settings
const IMAGE_CONFIG = {
  // Maximum dimensions
  product: {
    maxWidth: 1200,
    maxHeight: 1200,
    quality: 85,
    format: 'webp' as const, // WebP for better compression
    fallbackFormat: 'jpeg' as const
  },
  thumbnail: {
    maxWidth: 400,
    maxHeight: 400,
    quality: 80,
    format: 'webp' as const,
    fallbackFormat: 'jpeg' as const
  },
  category: {
    maxWidth: 800,
    maxHeight: 600,
    quality: 85,
    format: 'webp' as const,
    fallbackFormat: 'jpeg' as const
  },
  logo: {
    maxWidth: 300,
    maxHeight: 100,
    quality: 90,
    format: 'png' as const, // PNG for logos to preserve transparency
    fallbackFormat: 'png' as const
  },
  favicon: {
    maxWidth: 64,
    maxHeight: 64,
    quality: 95,
    format: 'png' as const,
    fallbackFormat: 'png' as const
  }
}

// Allowed image types
const ALLOWED_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif']
const MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB

export interface UploadedImage {
  url: string
  thumbnailUrl?: string
  width: number
  height: number
  size: number
  format: string
}

/**
 * Validate image file
 */
export function validateImage(file: any): { valid: boolean; error?: string } {
  if (!file) {
    return { valid: false, error: 'لم يتم تحديد ملف' }
  }

  if (!ALLOWED_TYPES.includes(file.mimetype)) {
    return {
      valid: false,
      error: 'نوع الملف غير مسموح. الأنواع المسموحة: JPG, PNG, WebP, GIF'
    }
  }

  if (file.size > MAX_FILE_SIZE) {
    return {
      valid: false,
      error: `حجم الملف كبير جداً. الحد الأقصى ${MAX_FILE_SIZE / 1024 / 1024}MB`
    }
  }

  return { valid: true }
}

/**
 * Generate unique filename
 */
function generateFileName(originalName: string, format: string = 'webp'): string {
  const timestamp = Date.now()
  const random = crypto.randomBytes(8).toString('hex')
  const ext = format.toLowerCase()
  const nameWithoutExt = path.parse(originalName).name
    .toLowerCase()
    .replace(/[^a-z0-9]/g, '-')
    .slice(0, 50) // Limit filename length

  return `${timestamp}-${random}-${nameWithoutExt}.${ext}`
}

/**
 * Get the correct base path for the application
 * Handles both regular builds and standalone builds
 */
function getBasePath(): string {
  const cwd = process.cwd()

  // Check if we're in a standalone build
  // Standalone builds have server.js in the current directory
  try {
    const serverJsPath = path.join(cwd, 'server.js')
    const standaloneServerExists = require('fs').existsSync(serverJsPath)

    if (standaloneServerExists) {
      console.log('🔍 Detected standalone Next.js build')
      // In standalone mode, we need to save to the standalone public directory
      // which matches nginx configuration: /www/wwwroot/node/ecommerc/.next/standalone/public/uploads/
      const standalonePublicPath = path.join(cwd, 'public')
      console.log('🔍 Using standalone public path:', standalonePublicPath)
      return cwd // Stay in standalone directory but use its public folder
    } else {
      console.log('🔍 Detected regular Next.js build')
      return cwd
    }
  } catch (error) {
    console.log('🔍 Using default build path')
    return cwd
  }
}

/**
 * Ensure upload directory exists
 */
async function ensureUploadDir(uploadPath: string): Promise<void> {
  try {
    await fs.access(uploadPath)
    console.log(`✅ Upload directory exists: ${uploadPath}`)
  } catch {
    console.log(`📁 Creating upload directory: ${uploadPath}`)
    try {
      await fs.mkdir(uploadPath, { recursive: true, mode: 0o755 })
      console.log(`✅ Upload directory created: ${uploadPath}`)
    } catch (mkdirError) {
      console.error(`❌ Failed to create upload directory: ${uploadPath}`, mkdirError)
      throw new Error(`فشل في إنشاء مجلد الرفع: ${uploadPath}`)
    }
  }

  // Verify write permissions
  try {
    const testFile = path.join(uploadPath, '.test-write')
    await fs.writeFile(testFile, 'test')
    await fs.unlink(testFile)
    console.log(`✅ Write permissions verified: ${uploadPath}`)
  } catch (permError) {
    console.error(`❌ No write permissions: ${uploadPath}`, permError)

    // Try to fix permissions
    try {
      await fs.chmod(uploadPath, 0o755)
      console.log(`🔧 Fixed permissions for: ${uploadPath}`)

      // Test again
      const testFile = path.join(uploadPath, '.test-write')
      await fs.writeFile(testFile, 'test')
      await fs.unlink(testFile)
      console.log(`✅ Write permissions verified after fix: ${uploadPath}`)
    } catch (fixError) {
      console.error(`❌ Could not fix permissions: ${uploadPath}`, fixError)
      throw new Error(`لا توجد صلاحيات الكتابة في مجلد الرفع: ${uploadPath}`)
    }
  }
}

/**
 * Process and optimize image
 */
export async function processImage(
  inputBuffer: Buffer,
  type: 'product' | 'category' | 'thumbnail' | 'logo' | 'favicon',
  originalName: string
): Promise<UploadedImage> {
  const config = IMAGE_CONFIG[type as keyof typeof IMAGE_CONFIG] || IMAGE_CONFIG.product

  console.log(`🖼️  Processing image: ${originalName} (${inputBuffer.length} bytes) for type: ${type}`)

  try {
    // Get image metadata
    console.log('📊 Getting image metadata...')
    const metadata = await sharp(inputBuffer).metadata()
    console.log(`✅ Image metadata: ${metadata.width}x${metadata.height}, ${metadata.format}`)

    // Process main image
    console.log('🔄 Processing image with Sharp...')
    let processedImage = sharp(inputBuffer)
      .resize(config.maxWidth, config.maxHeight, {
        fit: 'inside',
        withoutEnlargement: true
      })

    // Apply format based on type
    if (config.format === 'png') {
      processedImage = processedImage.png({ quality: config.quality })
    } else if (config.format === 'webp') {
      processedImage = processedImage.webp({ quality: config.quality })
    } else {
      processedImage = processedImage.jpeg({ quality: (config as any).quality || 85 })
    }

    // Generate filename
    const fileName = generateFileName(originalName, config.format)
    const uploadDir = type === 'category' ? 'categories' :
                      type === 'logo' || type === 'favicon' ? 'branding' : 'products'

    // Use the correct base path for different build types
    const basePath = getBasePath()
    const uploadPath = path.join(basePath, 'public', 'uploads', uploadDir)

    console.log(`📁 Upload path: ${uploadPath}`)
    console.log(`📄 Generated filename: ${fileName}`)

    // Ensure directory exists
    await ensureUploadDir(uploadPath)

    // Save processed image
    const filePath = path.join(uploadPath, fileName)
    console.log(`💾 Saving to: ${filePath}`)

    const outputInfo = await processedImage.toFile(filePath)
    console.log(`✅ Image saved: ${outputInfo.size} bytes, ${outputInfo.width}x${outputInfo.height}`)

    // Create relative URL for database
    const url = `/uploads/${uploadDir}/${fileName}`
    console.log(`🔗 Generated URL: ${url}`)

    return {
      url,
      width: outputInfo.width || config.maxWidth,
      height: outputInfo.height || config.maxHeight,
      size: outputInfo.size || 0,
      format: outputInfo.format || config.format
    }
  } catch (error) {
    console.error('❌ Error processing image:', error)
    console.error('📋 Error details:', {
      type,
      originalName,
      bufferSize: inputBuffer.length,
      errorMessage: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    })
    throw new Error(`فشل في معالجة الصورة: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`)
  }
}

/**
 * Process multiple images
 */
export async function processMultipleImages(
  files: any[],
  type: 'product' | 'category' | 'logo' | 'favicon' = 'product'
): Promise<UploadedImage[]> {
  const processedImages: UploadedImage[] = []

  for (const file of files) {
    // Validate each file
    const validation = validateImage(file)
    if (!validation.valid) {
      throw new Error(validation.error)
    }

    // Process image
    const processed = await processImage(file.buffer, type, file.originalname)
    processedImages.push(processed)

    // Also create thumbnail for products
    if (type === 'product') {
      const thumbnail = await processImage(file.buffer, 'thumbnail', file.originalname)
      processedImages[processedImages.length - 1].thumbnailUrl = thumbnail.url
    }
  }

  return processedImages
}

/**
 * Delete image file
 */
export async function deleteImage(imageUrl: string): Promise<void> {
  if (!imageUrl || !imageUrl.startsWith('/uploads/')) {
    return // Only delete our uploaded images
  }

  try {
    const basePath = getBasePath()
    const filePath = path.join(basePath, 'public', imageUrl)
    await fs.unlink(filePath)
    console.log(`✅ Deleted image: ${filePath}`)
  } catch (error) {
    console.error('Error deleting image:', error)
    // Don't throw error if file doesn't exist
  }
}

/**
 * Delete multiple images
 */
export async function deleteMultipleImages(imageUrls: string[]): Promise<void> {
  await Promise.all(imageUrls.map(url => deleteImage(url)))
}

/**
 * Convert base64 to buffer
 */
export function base64ToBuffer(base64String: string): Buffer {
  // Remove data URL prefix if present
  const base64Data = base64String.replace(/^data:image\/\w+;base64,/, '')
  return Buffer.from(base64Data, 'base64')
}

/**
 * Process base64 image
 */
export async function processBase64Image(
  base64String: string,
  type: 'product' | 'category' = 'product',
  filename: string = 'image'
): Promise<UploadedImage> {
  const buffer = base64ToBuffer(base64String)
  return processImage(buffer, type, filename)
}

/**
 * Get upload statistics for monitoring
 */
export async function getUploadStats(): Promise<{
  totalImages: number
  totalSize: number
  productImages: number
  categoryImages: number
  basePath: string
}> {
  const basePath = getBasePath()
  const uploadsPath = path.join(basePath, 'public', 'uploads')

  let totalImages = 0
  let totalSize = 0
  let productImages = 0
  let categoryImages = 0

  try {
    // Check products directory
    const productsPath = path.join(uploadsPath, 'products')
    try {
      const productFiles = await fs.readdir(productsPath)
      productImages = productFiles.length

      for (const file of productFiles) {
        const filePath = path.join(productsPath, file)
        const stats = await fs.stat(filePath)
        totalSize += stats.size
      }
    } catch (error) {
      console.log('Products directory not accessible:', error instanceof Error ? error.message : String(error))
    }

    // Check categories directory
    const categoriesPath = path.join(uploadsPath, 'categories')
    try {
      const categoryFiles = await fs.readdir(categoriesPath)
      categoryImages = categoryFiles.length

      for (const file of categoryFiles) {
        const filePath = path.join(categoriesPath, file)
        const stats = await fs.stat(filePath)
        totalSize += stats.size
      }
    } catch (error) {
      console.log('Categories directory not accessible:', error instanceof Error ? error.message : String(error))
    }

    totalImages = productImages + categoryImages

  } catch (error) {
    console.error('Error getting upload stats:', error)
  }

  return {
    totalImages,
    totalSize,
    productImages,
    categoryImages,
    basePath
  }
}
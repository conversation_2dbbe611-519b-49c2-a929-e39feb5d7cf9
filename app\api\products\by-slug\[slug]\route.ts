import { NextRequest, NextResponse } from 'next/server'
import prisma from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ slug: string }> }
) {
  try {
    const params = await context.params
    const { slug } = params

    if (!slug) {
      return NextResponse.json(
        { error: 'Slug is required' },
        { status: 400 }
      )
    }

    // Fetch product by slug
    const product = await prisma.product.findUnique({
      where: { slug },
      include: {
        category: true,
        brand: true,
        productvariant: {
          orderBy: { price: 'asc' }
        },
        productattribute: true,
        review: {
          where: { status: 'approved' },
          include: {
            user: {
              select: {
                nameAr: true,
                nameEn: true
              }
            }
          },
          orderBy: { createdAt: 'desc' }
        }
      }
    })

    if (!product) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      )
    }

    const response = NextResponse.json(product)

    // AGGRESSIVE cache prevention with timestamp
    response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0')
    response.headers.set('Pragma', 'no-cache')
    response.headers.set('Expires', 'Thu, 01 Jan 1970 00:00:00 GMT')
    response.headers.set('Last-Modified', new Date().toUTCString())
    response.headers.set('X-Data-Timestamp', Date.now().toString())
    response.headers.set('X-Cache-Prevention', 'true')

    return response
  } catch (error) {
    console.error('Error fetching product by slug:', error)
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    return NextResponse.json(
      { error: 'Failed to fetch product', details: errorMessage },
      { status: 500 }
    )
  }
}
import { NextRequest, NextResponse } from 'next/server'
import { processMultipleImages, validateImage, deleteImage } from '@/lib/image-upload'

export async function POST(request: NextRequest) {
  try {
    console.log('📤 Upload request received')

    const formData = await request.formData()
    const files = formData.getAll('files') as File[]
    const type = formData.get('type') as 'product' | 'category' | 'logo' | 'favicon' || 'product'

    console.log(`📁 Upload type: ${type}`)
    console.log(`📎 Files received: ${files.length}`)

    if (!files || files.length === 0) {
      console.log('❌ No files provided')
      return NextResponse.json(
        { error: 'لم يتم تحديد ملفات للرفع' },
        { status: 400 }
      )
    }

    // Convert Files to buffer format for processing
    console.log('🔄 Converting files to buffers...')
    const fileBuffers = await Promise.all(
      files.map(async (file, index) => {
        console.log(`📄 Processing file ${index + 1}: ${file.name} (${file.size} bytes, ${file.type})`)
        try {
          const buffer = Buffer.from(await file.arrayBuffer())
          console.log(`✅ File ${index + 1} converted to buffer (${buffer.length} bytes)`)
          return {
            buffer,
            originalname: file.name,
            mimetype: file.type,
            size: file.size
          }
        } catch (error) {
          console.error(`❌ Error converting file ${index + 1}:`, error)
          throw new Error(`فشل في معالجة الملف ${file.name}`)
        }
      })
    )

    // Validate all files first
    console.log('🔍 Validating files...')
    for (const [index, file] of fileBuffers.entries()) {
      console.log(`🔎 Validating file ${index + 1}: ${file.originalname}`)
      const validation = validateImage(file)
      if (!validation.valid) {
        console.log(`❌ Validation failed for ${file.originalname}: ${validation.error}`)
        return NextResponse.json(
          { error: validation.error },
          { status: 400 }
        )
      }
      console.log(`✅ File ${index + 1} validation passed`)
    }

    // Process and optimize images
    console.log('⚙️  Processing images...')
    try {
      const processedImages = await processMultipleImages(fileBuffers, type)
      console.log(`✅ Successfully processed ${processedImages.length} images`)

      const response = NextResponse.json({
        success: true,
        images: processedImages,
        message: `تم رفع ${processedImages.length} صورة بنجاح`
      })

      // Add minimal cache headers for successful uploads
      response.headers.set('Cache-Control', 'no-cache')
      response.headers.set('X-Upload-Success', 'true')

      return response

    } catch (processError) {
      console.error('❌ Image processing error:', processError)
      return NextResponse.json(
        { error: processError instanceof Error ? processError.message : 'فشل في معالجة الصور' },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('📤 Upload error:', error)

    // Provide more detailed error information
    let errorMessage = 'فشل في رفع الصور'
    let errorDetails = ''

    if (error instanceof Error) {
      errorMessage = error.message
      errorDetails = error.stack || ''
    }

    // Log additional context for debugging
    console.error('📋 Error details:', {
      message: errorMessage,
      details: errorDetails,
      timestamp: new Date().toISOString(),
      nodeVersion: process.version,
      platform: process.platform
    })

    return NextResponse.json(
      {
        error: errorMessage,
        details: process.env.NODE_ENV === 'development' ? errorDetails : undefined,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}

// DELETE endpoint to remove images
export async function DELETE(request: NextRequest) {
  try {
    const { imageUrl } = await request.json()

    if (!imageUrl) {
      return NextResponse.json(
        { error: 'لم يتم تحديد الصورة للحذف' },
        { status: 400 }
      )
    }

    await deleteImage(imageUrl)

    return NextResponse.json({
      success: true,
      message: 'تم حذف الصورة بنجاح'
    })

  } catch (error) {
    console.error('Delete error:', error)
    return NextResponse.json(
      { error: 'فشل في حذف الصورة' },
      { status: 500 }
    )
  }
}
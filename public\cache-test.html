<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cache Test - Woo2</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .headers {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .warning {
            color: #ffc107;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>🔍 Woo2 Cache Prevention Test</h1>
    <p>This page tests if our aggressive cache prevention is working correctly.</p>

    <div class="test-section">
        <h2>📡 API Response Headers Test</h2>
        <button onclick="testAPI('/api/products')">Test Products API</button>
        <button onclick="testAPI('/api/categories')">Test Categories API</button>
        <button onclick="testAPI('/api/settings')">Test Settings API</button>
        <div id="headers-test" class="headers">
            Click a button to test API response headers...
        </div>
    </div>

    <div class="test-section">
        <h2>🔄 Page Headers Test</h2>
        <button onclick="testPageHeaders()">Test Page Headers</button>
        <div id="page-headers-test" class="headers">
            Click to test current page headers...
        </div>
    </div>

    <div class="test-section">
        <h2>🎯 Expected Results</h2>
        <p>For cache prevention to work correctly, you should see:</p>
        <ul>
            <li><span class="success">✅ Cache-Control: no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0</span></li>
            <li><span class="success">✅ Pragma: no-cache</span></li>
            <li><span class="success">✅ Expires: Thu, 01 Jan 1970 00:00:00 GMT</span></li>
            <li><span class="success">✅ X-Cache-Prevention: true</span></li>
            <li><span class="success">✅ X-Data-Timestamp: [current timestamp]</span></li>
        </ul>
    </div>

    <script>
        async function testAPI(endpoint) {
            try {
                const response = await fetch(endpoint);
                const headers = response.headers;

                let result = `🔍 Testing: ${endpoint}\n`;
                result += `Status: ${response.status}\n\n`;
                result += `📋 Response Headers:\n`;

                // Check for cache prevention headers
                const cacheControl = headers.get('cache-control');
                const pragma = headers.get('pragma');
                const expires = headers.get('expires');
                const cachePrevention = headers.get('x-cache-prevention');
                const dataTimestamp = headers.get('x-data-timestamp');

                result += `Cache-Control: ${cacheControl}\n`;
                result += `Pragma: ${pragma}\n`;
                result += `Expires: ${expires}\n`;
                result += `X-Cache-Prevention: ${cachePrevention}\n`;
                result += `X-Data-Timestamp: ${dataTimestamp}\n\n`;

                // Validate results
                result += `✅ Validation:\n`;
                if (cacheControl && cacheControl.includes('no-store')) {
                    result += `✅ Cache-Control is properly set\n`;
                } else {
                    result += `❌ Cache-Control missing or incorrect\n`;
                }

                if (pragma === 'no-cache') {
                    result += `✅ Pragma is properly set\n`;
                } else {
                    result += `❌ Pragma missing or incorrect\n`;
                }

                if (cachePrevention === 'true') {
                    result += `✅ X-Cache-Prevention is set\n`;
                } else {
                    result += `❌ X-Cache-Prevention missing\n`;
                }

                if (dataTimestamp) {
                    result += `✅ X-Data-Timestamp is present\n`;
                } else {
                    result += `❌ X-Data-Timestamp missing\n`;
                }

                document.getElementById('headers-test').textContent = result;

            } catch (error) {
                document.getElementById('headers-test').textContent = `❌ Error: ${error.message}`;
            }
        }

        function testPageHeaders() {
            const headers = {
                'Cache-Control': document.querySelector('meta[name="cache-control"]')?.content || 'Not found',
                'Last-Modified': new Date().toUTCString(),
                'X-Cache-Prevention': 'Check network tab'
            };

            let result = `🔍 Page Headers Test:\n\n`;
            result += `Cache-Control: ${performance.getEntriesByType('navigation')[0]?.responseHeaders?.['cache-control'] || 'Check network tab in dev tools'}\n`;
            result += `Last-Modified: ${new Date().toUTCString()}\n`;
            result += `X-Cache-Prevention: Check network tab in browser dev tools\n\n`;
            result += `💡 Tip: Open Network tab → Refresh page → Check Response Headers\n`;

            document.getElementById('page-headers-test').textContent = result;
        }

        // Auto-test on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                testAPI('/api/products');
            }, 1000);
        });
    </script>
</body>
</html>

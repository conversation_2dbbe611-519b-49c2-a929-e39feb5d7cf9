import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import { requireAdmin } from '@/lib/auth'
import { revalidatePath } from 'next/cache'
import crypto from 'crypto'

const prisma = new PrismaClient()

// GET single product
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params

    // Try to find by ID first, then by slug
    let product = await prisma.product.findUnique({
      where: { id: resolvedParams.id },
      include: {
        category: {
          select: {
            id: true,
            nameAr: true,
            nameEn: true
          }
        },
        brand: {
          select: {
            id: true,
            nameAr: true,
            nameEn: true
          }
        },
        productvariant: true,
        _count: {
          select: {
            orderitem: true,
            cartitem: true,
            favoriteitem: true
          }
        }
      }
    })

    // If not found by ID, try by slug
    if (!product) {
      product = await prisma.product.findUnique({
        where: { slug: resolvedParams.id },
        include: {
          category: {
            select: {
              id: true,
              nameAr: true,
              nameEn: true
            }
          },
          brand: {
            select: {
              id: true,
              nameAr: true,
              nameEn: true
            }
          },
          productvariant: true,
          _count: {
            select: {
              orderitem: true,
              cartitem: true,
              favoriteitem: true
            }
          }
        }
      })
    }

    if (!product) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      )
    }

    const response = NextResponse.json(product)

    // AGGRESSIVE cache prevention with timestamp
    response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0')
    response.headers.set('Pragma', 'no-cache')
    response.headers.set('Expires', 'Thu, 01 Jan 1970 00:00:00 GMT')
    response.headers.set('Last-Modified', new Date().toUTCString())
    response.headers.set('X-Data-Timestamp', Date.now().toString())
    response.headers.set('X-Cache-Prevention', 'true')

    return response
  } catch (error) {
    console.error('Error fetching product:', error)
    return NextResponse.json(
      { error: 'Failed to fetch product' },
      { status: 500 }
    )
  }
}

// PATCH update product (for simple updates like productType)
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Require admin authentication
    const authResult = await requireAdmin(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Not authenticated' ? 401 : 403 }
      )
    }

    const resolvedParams = await params
    const data = await request.json()

    console.log('PATCH request for product:', resolvedParams.id, 'with data:', data)

    // Simple update for specific fields like productType
    const updateData: any = {}

    // Only allow specific fields for PATCH to avoid conflicts
    if (data.productType !== undefined) {
      updateData.productType = data.productType
    }
    if (data.status !== undefined) {
      updateData.status = data.status
    }
    if (data.featured !== undefined) {
      updateData.featured = data.featured
    }

    const product = await prisma.product.update({
      where: { id: resolvedParams.id },
      data: updateData,
      include: {
        category: {
          select: {
            id: true,
            nameAr: true,
            nameEn: true
          }
        },
        brand: {
          select: {
            id: true,
            nameAr: true,
            nameEn: true
          }
        },
        productvariant: true,
        _count: {
          select: {
            orderitem: true,
            cartitem: true,
            favoriteitem: true
          }
        }
      }
    })

    // Revalidate pages that display products to clear cache
    revalidatePath('/')  // Home page
    revalidatePath('/products')  // Products page
    revalidatePath('/shop')  // Shop page

    return NextResponse.json(product)
  } catch (error) {
    console.error('Error updating product:', error)
    return NextResponse.json(
      { error: 'Failed to update product' },
      { status: 500 }
    )
  }
}

// PUT update product
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Require admin authentication
    const authResult = await requireAdmin(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Not authenticated' ? 401 : 403 }
      )
    }

    const resolvedParams = await params
    const data = await request.json()

    console.log('Updating product:', resolvedParams.id)
    console.log('Variants data:', JSON.stringify(data.productvariant, null, 2))

    // If name is being updated, update slug too
    const updateData: any = { ...data }

    if (data.nameEn || data.nameAr) {
      const baseSlug = (data.nameEn || data.nameAr)
        .toLowerCase()
        .replace(/[^\w\s\u0600-\u06FF]/gi, '')
        .replace(/\s+/g, '-')

      // Check if slug exists and make it unique (excluding current product)
      let slug = baseSlug
      let counter = 1
      while (true) {
        const existing = await prisma.product.findUnique({ where: { slug } })
        if (!existing || existing.id === resolvedParams.id) break
        slug = `${baseSlug}-${counter}`
        counter++
      }
      updateData.slug = slug
    }

    // Handle SKU - check for uniqueness if provided
    if (updateData.sku !== undefined) {
      if (updateData.sku === '' || updateData.sku === null) {
        updateData.sku = null // Allow null/empty SKU
      } else {
        // Check if SKU is already used by another product
        const existingProduct = await prisma.product.findUnique({
          where: { sku: updateData.sku }
        })

        if (existingProduct && existingProduct.id !== resolvedParams.id) {
          return NextResponse.json(
            { error: `SKU "${updateData.sku}" is already used by another product` },
            { status: 400 }
          )
        }
      }
    }

    // Handle price for products with variants
    if (updateData.price !== undefined) {
      let productPrice = updateData.price ? parseFloat(updateData.price) : null

      // If product has variants and no base price, use the minimum variant price
      if (data.productvariant && data.productvariant.length > 0 && (!productPrice || isNaN(productPrice))) {
        const variantPrices = data.productvariant.map((v: any) => v.price).filter((p: any) => p && !isNaN(p))
        productPrice = variantPrices.length > 0 ? Math.min(...variantPrices) : 0
      }

      // If still no valid price, default to 0
      if (!productPrice || isNaN(productPrice)) {
        productPrice = 0
      }

      updateData.price = productPrice
    }

    // Convert other numeric fields
    if (updateData.compareAtPrice !== undefined) updateData.compareAtPrice = updateData.compareAtPrice ? parseFloat(updateData.compareAtPrice) : null
    if (updateData.cost !== undefined) updateData.cost = updateData.cost ? parseFloat(updateData.cost) : null
    if (updateData.stockQuantity !== undefined) updateData.stockQuantity = updateData.productType === 'virtual' ? 0 : parseInt(updateData.stockQuantity || '0')
    if (updateData.weight !== undefined) updateData.weight = updateData.weight ? parseFloat(updateData.weight) : null

    // Handle virtual product fields
    if (updateData.productType === 'virtual') {
      updateData.manageStock = false
      updateData.stockQuantity = 0
      updateData.allowBackorder = false
      updateData.inStock = true

      if (updateData.workshopDate !== undefined) {
        updateData.workshopDate = updateData.workshopDate ? new Date(updateData.workshopDate) : null
      }
      if (updateData.workshopCapacity !== undefined) {
        updateData.workshopCapacity = updateData.workshopCapacity ? parseInt(updateData.workshopCapacity) : null
      }
    }

    // Handle images - ensure it's always a valid JSON string array
    if (updateData.images !== undefined) {
      if (!updateData.images || (Array.isArray(updateData.images) && updateData.images.length === 0)) {
        updateData.images = JSON.stringify(['/images/product-placeholder.svg'])
      } else if (Array.isArray(updateData.images)) {
        // Filter out blob URLs and keep only valid image paths
        const validImages = updateData.images.filter((img: string) =>
          img && !img.startsWith('blob:')
        )
        updateData.images = JSON.stringify(
          validImages.length > 0 ? validImages : ['/images/product-placeholder.svg']
        )
      } else if (typeof updateData.images !== 'string') {
        updateData.images = JSON.stringify(updateData.images)
      }
    }

    // Handle categoryId - convert to relation update
    if (updateData.categoryId !== undefined) {
      if (updateData.categoryId) {
        updateData.category = {
          connect: { id: updateData.categoryId }
        }
      } else {
        updateData.category = {
          disconnect: true
        }
      }
      delete updateData.categoryId
    }

    // Handle brandId - convert to relation update
    if (updateData.brandId !== undefined) {
      if (updateData.brandId) {
        updateData.brand = {
          connect: { id: updateData.brandId }
        }
      } else {
        updateData.brand = {
          disconnect: true
        }
      }
      delete updateData.brandId
    }

    // Remove fields that shouldn't be in updateData
    delete updateData.trackQuantity
    delete updateData.quantity
    delete updateData.allowBackorder

    // Handle variants update
    let variantsUpdate = {}
    if (data.productvariant !== undefined) {
      console.log('Processing variants update. Received variants:', data.productvariant)
      // Delete all existing variants and create new ones
      // This is simpler than trying to update/create/delete selectively

      if (data.productvariant.length > 0) {
        const variantsToCreate = data.productvariant.map((variant: any, index: number) => {

          // Handle variant SKU - if empty, set to null to avoid unique constraint issues
          let variantSku = variant.sku || null
          if (variantSku === '') {
            variantSku = null
          }

          const variantData = {
            id: crypto.randomUUID(), // Add required id field
            size: variant.size || '',
            sizeEn: variant.sizeEn || variant.size || '',
            price: variant.price !== undefined && variant.price !== null && variant.price !== ''
              ? parseFloat(String(variant.price))
              : 0,
            originalPrice: variant.originalPrice && variant.originalPrice !== ''
              ? parseFloat(String(variant.originalPrice))
              : null,
            sku: variantSku,
            manageStock: Boolean(variant.manageStock),
            stockQuantity: variant.stockQuantity !== undefined && variant.stockQuantity !== null && variant.stockQuantity !== ''
              ? parseInt(String(variant.stockQuantity))
              : 0,
            inStock: variant.inStock !== undefined ? Boolean(variant.inStock) : true,
            createdAt: new Date(),
            updatedAt: new Date()
          }

          console.log(`Creating variant ${index + 1}:`, variantData)
          return variantData
        })

        variantsUpdate = {
          deleteMany: {}, // Delete all existing variants
          create: variantsToCreate
        }
      } else {
        // If variants array is empty, just delete all existing variants
        variantsUpdate = {
          deleteMany: {}
        }
      }

      // Remove variants from updateData as it will be handled separately
      delete updateData.variants
      delete updateData.productvariant
    }

    console.log('Variant update object:', variantsUpdate)

    // Test if we can create a variant directly
    if (Object.keys(variantsUpdate).length > 0 && (variantsUpdate as any).create) {
      try {
        console.log('Testing direct variant creation for product:', resolvedParams.id)
        const createData = (variantsUpdate as any).create[0]
        console.log('Test variant data:', createData)
        const testVariant = await prisma.productvariant.create({
          data: {
            productId: resolvedParams.id,
            ...createData
          }
        })
        console.log('Test variant created successfully:', testVariant)
        // Delete the test variant
        await prisma.productvariant.delete({ where: { id: testVariant.id } })
      } catch (testError) {
        console.error('Direct variant creation test failed:', testError)
      }
    }

    console.log('Final update data being sent to Prisma:')
    console.log('Update data keys:', Object.keys(updateData))
    console.log('Has variant update:', Object.keys(variantsUpdate).length > 0)

    const finalData = {
      ...updateData,
      ...(Object.keys(variantsUpdate).length > 0 ? { productvariant: variantsUpdate } : {})
    }
    console.log('Final data with variants:', JSON.stringify(finalData, null, 2))

    const product = await prisma.product.update({
      where: { id: resolvedParams.id },
      data: finalData,
      include: {
        category: {
          select: {
            id: true,
            nameAr: true,
            nameEn: true
          }
        },
        brand: {
          select: {
            id: true,
            nameAr: true,
            nameEn: true
          }
        },
        productvariant: true
      }
    })

    console.log('Updated product variants:', product.productvariant)

    // Revalidate pages that display products to clear cache
    revalidatePath('/')  // Home page
    revalidatePath('/products')  // Products page
    revalidatePath('/shop')  // Shop page
    revalidatePath(`/products/${product.slug}`)  // Specific product page
    return NextResponse.json(product)
  } catch (error: any) {
    console.error('Error updating product:', error)
    console.error('Error details:', error.message)
    if (error.code === 'P2002') {
      return NextResponse.json(
        { error: 'Product with this SKU or slug already exists' },
        { status: 400 }
      )
    }
    return NextResponse.json(
      { error: error.message || 'Failed to update product' },
      { status: 500 }
    )
  }
}

// DELETE product
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Require admin authentication
    const authResult = await requireAdmin(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Not authenticated' ? 401 : 403 }
      )
    }
    const resolvedParams = await params
    // Check if product is used in any orders
    const orderItems = await prisma.orderitem.count({
      where: { productId: resolvedParams.id }
    })

    if (orderItems > 0) {
      return NextResponse.json(
        { error: 'Cannot delete product that has been ordered' },
        { status: 400 }
      )
    }

    // Delete related cart and favorite items first
    await prisma.cartitem.deleteMany({
      where: { productId: resolvedParams.id }
    })

    await prisma.favoriteitem.deleteMany({
      where: { productId: resolvedParams.id }
    })

    // Delete the product
    await prisma.product.delete({
      where: { id: resolvedParams.id }
    })

    // Revalidate pages that display products to clear cache
    revalidatePath('/')  // Home page
    revalidatePath('/products')  // Products page
    revalidatePath('/shop')  // Shop page

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting product:', error)
    return NextResponse.json(
      { error: 'Failed to delete product' },
      { status: 500 }
    )
  }
}
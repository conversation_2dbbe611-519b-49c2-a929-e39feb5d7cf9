<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Test - Woo2</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .upload-area {
            border: 2px dashed #007bff;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            transition: all 0.3s;
        }
        .upload-area:hover {
            border-color: #0056b3;
            background: #f8f9ff;
        }
        .upload-area.dragover {
            border-color: #28a745;
            background: #f8fff8;
        }
        input[type="file"] {
            display: none;
        }
        .upload-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .upload-btn:hover {
            background: #0056b3;
        }
        .results {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .warning {
            color: #ffc107;
            font-weight: bold;
        }
        .progress {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            margin: 10px 0;
            overflow: hidden;
        }
        .progress-bar {
            height: 100%;
            background: #007bff;
            width: 0%;
            transition: width 0.3s;
        }
    </style>
</head>
<body>
    <h1>🖼️ Woo2 Upload Test</h1>
    <p>Test image upload functionality and debug any issues.</p>

    <div class="test-section">
        <h2>📤 Upload Test</h2>
        <form id="uploadForm">
            <div class="upload-area" id="uploadArea">
                <div>
                    <p>📎 Drop images here or click to browse</p>
                    <input type="file" id="fileInput" name="files" multiple accept="image/*">
                    <br><br>
                    <label>
                        <input type="radio" name="type" value="product" checked> Product Images
                    </label>
                    <label>
                        <input type="radio" name="type" value="category"> Category Images
                    </label>
                    <label>
                        <input type="radio" name="type" value="logo"> Logo
                    </label>
                    <br><br>
                    <button type="submit" class="upload-btn">🚀 Upload Images</button>
                </div>
            </div>
        </form>

        <div class="progress" id="progress" style="display: none;">
            <div class="progress-bar" id="progressBar"></div>
        </div>
    </div>

    <div class="test-section">
        <h2>📊 Results</h2>
        <div id="results" class="results">
            Results will appear here...
        </div>
        <button onclick="clearResults()" class="upload-btn">🗑️ Clear Results</button>
    </div>

    <div class="test-section">
        <h2>🔍 System Information</h2>
        <div id="systemInfo" class="results">
            Loading system information...
        </div>
    </div>

    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const results = document.getElementById('results');
        const progress = document.getElementById('progress');
        const progressBar = document.getElementById('progressBar');

        // Drag and drop functionality
        uploadArea.addEventListener('click', () => fileInput.click());
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = Array.from(e.dataTransfer.files);
            handleFiles(files);
        });

        fileInput.addEventListener('change', (e) => {
            handleFiles(Array.from(e.target.files));
        });

        function handleFiles(files) {
            console.log('Files selected:', files);
            // Files are handled by the form submission
        }

        // Form submission
        document.getElementById('uploadForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const formData = new FormData(e.target);
            const files = formData.getAll('files');

            if (files.length === 0) {
                logResult('❌ No files selected', 'error');
                return;
            }

            logResult(`📤 Starting upload of ${files.length} files...`, 'info');

            // Show progress
            progress.style.display = 'block';
            progressBar.style.width = '0%';

            try {
                const response = await fetch('/api/upload', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    logResult(`✅ Upload successful!`, 'success');
                    logResult(`📊 Response: ${JSON.stringify(data, null, 2)}`, 'info');

                    // Update progress to 100%
                    progressBar.style.width = '100%';

                    // Display uploaded images
                    if (data.images && data.images.length > 0) {
                        logResult('🖼️ Uploaded Images:', 'info');
                        data.images.forEach((img, index) => {
                            logResult(`  ${index + 1}. ${img.url} (${img.width}x${img.height})`, 'info');
                        });
                    }
                } else {
                    logResult(`❌ Upload failed: ${data.error}`, 'error');
                    if (data.details) {
                        logResult(`📋 Error details: ${data.details}`, 'warning');
                    }
                    logResult(`📊 Full response: ${JSON.stringify(data, null, 2)}`, 'warning');
                }

            } catch (error) {
                logResult(`❌ Network error: ${error.message}`, 'error');
                console.error('Upload error:', error);
            }

            // Hide progress after a delay
            setTimeout(() => {
                progress.style.display = 'none';
                progressBar.style.width = '0%';
            }, 2000);
        });

        function logResult(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            results.textContent += logEntry;

            // Auto scroll to bottom
            results.scrollTop = results.scrollHeight;

            // Add color class to the results div
            results.className = `results ${type}`;
        }

        function clearResults() {
            results.textContent = 'Results will appear here...';
            results.className = 'results';
        }

        // Load system information
        window.addEventListener('load', async () => {
            try {
                const response = await fetch('/api/settings');
                const settings = await response.json();

                let sysInfo = '🌐 System Information:\n';
                sysInfo += `📡 API Status: ${response.ok ? '✅ Working' : '❌ Failed'}\n`;
                sysInfo += `🔗 URL: ${window.location.origin}\n`;
                sysInfo += `🌍 User Agent: ${navigator.userAgent}\n`;
                sysInfo += `📱 Platform: ${navigator.platform}\n`;
                sysInfo += `💾 Cookie Enabled: ${navigator.cookieEnabled ? '✅' : '❌'}\n`;

                document.getElementById('systemInfo').textContent = sysInfo;
            } catch (error) {
                document.getElementById('systemInfo').textContent = `❌ Failed to load system info: ${error.message}`;
            }
        });
    </script>
</body>
</html>

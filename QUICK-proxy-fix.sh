#!/bin/bash

echo "🔧 Quick Fix for Proxy + Localhost Setup"
echo "========================================"

echo "🎯 Your Setup:"
echo "• Next.js app runs on localhost:3000"
echo "• Nginx proxy forwards horseoud.com → localhost:3000"
echo "• Problem: ALL requests (including static files) are proxied"
echo ""

echo "⚡ The Fix:"
echo "• Static files: Serve directly from nginx"
echo "• Dynamic requests: Proxy to localhost"
echo ""

echo "📋 Commands to run on your production server:"
echo "=============================================="
echo ""

echo "1. 📁 Check your app is running:"
echo "   ps aux | grep node"
echo "   netstat -tulpn | grep :3000"
echo ""

echo "2. 📝 Backup current nginx config:"
echo "   sudo cp /etc/nginx/sites-available/horseoud /etc/nginx/sites-available/horseoud.backup"
echo ""

echo "3. 🔧 Edit nginx configuration:"
echo "   sudo nano /etc/nginx/sites-available/horseoud"
echo "   # Replace with content from CORRECT-proxy-nginx.conf"
echo ""

echo "4. ✅ Test the configuration:"
echo "   sudo nginx -t"
echo ""

echo "5. 🚀 Apply the fix:"
echo "   sudo systemctl reload nginx"
echo ""

echo "6. 🧪 Test static assets:"
echo "   curl -I https://horseoud.com/_next/static/chunks/2619-04bc32f026a0d946.js"
echo "   # Should return: HTTP/2 200 (not 502)"
echo ""

echo "7. 🧪 Test dynamic content:"
echo "   curl -I https://horseoud.com/"
echo "   # Should still return: HTTP/2 200"
echo ""

echo "🎯 Key nginx configuration sections:"
echo "===================================="
echo ""

echo "Static assets (serve directly):"
echo "location /_next/static/ {"
echo "    alias /www/wwwroot/node/ecommerc/.next/standalone/.next/static/;"
echo "    expires 1y;"
echo "    try_files \$uri =404;"
echo "}"
echo ""

echo "Dynamic requests (proxy to localhost):"
echo "location / {"
echo "    proxy_pass http://127.0.0.1:3000;"
echo "    proxy_set_header Host \$host;"
echo "    # ... other proxy headers"
echo "}"
echo ""

echo "✅ Success indicators:"
echo "====================="
echo "• No 502 errors in browser console"
echo "• CSS loads and pages look styled"
echo "• JavaScript works properly"
echo "• Static assets show 200 status codes"

echo ""
echo "🆘 If still broken:"
echo "=================="
echo "1. Check nginx error log:"
echo "   sudo tail -f /var/log/nginx/error.log"
echo ""
echo "2. Verify file paths exist:"
echo "   ls -la /www/wwwroot/node/ecommerc/.next/standalone/.next/static/"
echo ""
echo "3. Check app is responding:"
echo "   curl http://localhost:3000"

echo ""
echo "🎉 After this fix:"
echo "=================="
echo "• Static files: Served fast by nginx"
echo "• Dynamic content: Handled by your Node.js app"
echo "• No more 502 errors!"
echo "• Website works properly!"

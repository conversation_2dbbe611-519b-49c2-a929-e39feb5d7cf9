import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import { requireAdmin } from '@/lib/auth'
import { revalidatePath } from 'next/cache'
import crypto from 'crypto'

const prisma = new PrismaClient()

// GET all products
export async function GET() {
  try {
    const products = await prisma.product.findMany({
      include: {
        category: {
          select: {
            id: true,
            nameAr: true,
            nameEn: true,
            slug: true
          }
        },
        brand: {
          select: {
            id: true,
            nameAr: true,
            nameEn: true,
            slug: true
          }
        },
        productvariant: true,
        _count: {
          select: {
            orderitem: true,
            cartitem: true,
            favoriteitem: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    const response = NextResponse.json(products)

    // AGGRESSIVE cache prevention with timestamp
    response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0')
    response.headers.set('Pragma', 'no-cache')
    response.headers.set('Expires', 'Thu, 01 Jan 1970 00:00:00 GMT')
    response.headers.set('Last-Modified', new Date().toUTCString())
    response.headers.set('X-Data-Timestamp', Date.now().toString())
    response.headers.set('X-Cache-Prevention', 'true')

    return response
  } catch (error) {
    console.error('Error fetching products:', error)
    return NextResponse.json(
      { error: 'Failed to fetch products' },
      { status: 500 }
    )
  }
}

// POST create new product
export async function POST(request: NextRequest) {
  try {
    // Require admin authentication
    const authResult = await requireAdmin(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Not authenticated' ? 401 : 403 }
      )
    }

    const data = await request.json()
    console.log('Received product data:', data)

    // Generate slug from Arabic or English name
    const baseSlug = (data.nameEn || data.nameAr)
      .toLowerCase()
      .replace(/[^\w\s\u0600-\u06FF]/gi, '')
      .replace(/\s+/g, '-')

    // Check if slug exists and make it unique
    let slug = baseSlug
    let counter = 1
    while (await prisma.product.findUnique({ where: { slug } })) {
      slug = `${baseSlug}-${counter}`
      counter++
    }

    // Handle price for products with variants
    let productPrice = data.price ? parseFloat(data.price) : null

    // If product has variants and no base price, use the minimum variant price
    if (data.variants && data.variants.length > 0 && (!productPrice || isNaN(productPrice))) {
      const variantPrices = data.variants.map((v: any) => v.price).filter((p: any) => p && !isNaN(p))
      productPrice = variantPrices.length > 0 ? Math.min(...variantPrices) : 0
    }

    // If still no valid price, default to 0
    if (!productPrice || isNaN(productPrice)) {
      productPrice = 0
    }

    // Handle SKU - check for uniqueness if provided
    let productSku = data.sku || null
    if (productSku === '') {
      productSku = null
    } else if (productSku) {
      // Check if SKU already exists
      const existingProduct = await prisma.product.findUnique({
        where: { sku: productSku }
      })

      if (existingProduct) {
        return NextResponse.json(
          { error: `SKU "${productSku}" is already used by another product` },
          { status: 400 }
        )
      }
    }

    // Prepare data for creation
    const createData: any = {
      nameAr: data.nameAr,
      nameEn: data.nameEn,
      slug,
      descriptionAr: data.descriptionAr || null,
      descriptionEn: data.descriptionEn || null,
      productType: data.productType || 'simple',
      price: productPrice,
      compareAtPrice: data.compareAtPrice && data.compareAtPrice !== '' ? parseFloat(data.compareAtPrice) : null,
      cost: data.cost && data.cost !== '' ? parseFloat(data.cost) : null,
      sku: productSku,
      barcode: data.barcode || null,
      manageStock: data.productType === 'virtual' ? false : (data.manageStock ?? false),
      stockQuantity: data.productType === 'virtual' ? 0 : parseInt(data.stockQuantity || '0'),
      allowBackorder: data.productType === 'virtual' ? false : (data.allowBackorder ?? false),
      weight: data.weight && data.weight !== '' ? parseFloat(data.weight) : null,
      unit: data.unit || null,
      origin: data.origin || null,
      status: data.status || 'active',
      featured: data.featured ?? false,
      bestseller: data.bestseller ?? false,
      onSale: data.onSale ?? false,
      inStock: data.productType === 'virtual' ? true : (data.inStock ?? true),
      images: data.images ? (typeof data.images === 'string' ? data.images : JSON.stringify(data.images)) : JSON.stringify(['/images/product-placeholder.svg']),
      tags: data.tags || null,
      metaTitle: data.metaTitle || null,
      metaDescription: data.metaDescription || null
    }

    // Add workshop fields for virtual products
    if (data.productType === 'virtual') {
      createData.workshopDate = data.workshopDate ? new Date(data.workshopDate) : null
      createData.workshopDuration = data.workshopDuration || null
      createData.workshopLocation = data.workshopLocation || null
      createData.workshopCapacity = data.workshopCapacity ? parseInt(data.workshopCapacity) : null
      createData.workshopAttendees = 0
    }

    // Handle category relation
    if (data.categoryId && data.categoryId !== '') {
      createData.category = {
        connect: { id: data.categoryId }
      }
    }

    // Handle brand relation
    if (data.brandId && data.brandId !== '') {
      createData.brand = {
        connect: { id: data.brandId }
      }
    }

    // Add variants if provided
    if (data.variants && data.variants.length > 0) {
      createData.productvariant = {
        create: data.variants.map((variant: any) => {
          // Handle variant SKU - if empty, set to null
          let variantSku = variant.sku || null
          if (variantSku === '') {
            variantSku = null
          }

          return {
            id: crypto.randomUUID(), // Add required id field
            size: variant.size,
            sizeEn: variant.sizeEn || variant.size,
            price: parseFloat(variant.price) || 0,
            originalPrice: variant.originalPrice ? parseFloat(variant.originalPrice) : null,
            sku: variantSku,
            manageStock: variant.manageStock ?? false,
            stockQuantity: parseInt(variant.stockQuantity || '0'),
            inStock: variant.inStock ?? true,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        })
      }
    }

    console.log('Creating product with data:', createData)

    const product = await prisma.product.create({
      data: createData,
      include: {
        category: {
          select: {
            id: true,
            nameAr: true,
            nameEn: true
          }
        },
        brand: {
          select: {
            id: true,
            nameAr: true,
            nameEn: true
          }
        },
        productvariant: true
      }
    })

    // Revalidate pages that display products to clear cache
    revalidatePath('/')  // Home page
    revalidatePath('/products')  // Products page
    revalidatePath('/shop')  // Shop page
    
    return NextResponse.json(product)
  } catch (error: any) {
    console.error('Error creating product - Full error:', error)
    console.error('Error message:', error.message)
    console.error('Error code:', error.code)

    // Return more detailed error information
    return NextResponse.json(
      {
        error: 'Failed to create product',
        details: error.message,
        code: error.code
      },
      { status: 500 }
    )
  }
}